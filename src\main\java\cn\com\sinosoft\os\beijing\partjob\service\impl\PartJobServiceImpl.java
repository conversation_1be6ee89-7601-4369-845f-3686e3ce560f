<%@ page language="java" contentType="text/html; charset=GBK" pageEncoding="GBK"%>
<%@ page import="ie.weaf.toolkit.Util"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
	<title>兼职申请 - 维护</title>
	<meta http-equiv="content-type" content="text/html; charset=GBK" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<%@ include file="/common/taglibs.jsp"%>
	<script type="text/javascript">
		var form;
		$(document).ready(function() {
			mini.parse();
			form = new mini.Form("form1");
			$("#znksTd1").show();
		});
		//保存
		function save(e) {
			form.validate();
			if (form.isValid() == false) {
				//提示验证错误信息，400为提示框宽度，300为提示框高度
				showFormErrorTexts(form.getErrorTexts(),400,300);
				form.getErrors()[0].focus();
				return;
			}
			mini.get("saveType").setValue(e);//提交方式
			if ("${pageState}" == "add") {
				document.form1.action = "${ctx}/cn/com/sinosoft/rs/partjob/partJob_addParentSubmit.ac";
			} else {
				document.form1.action = "${ctx}/cn/com/sinosoft/rs/partjob/partJob_edtParentSubmit.ac";
			}
			document.form1.submit();
			waitClick();
		}
		
		//打开审核窗口
		function toSubmit(type){
			mini.get("auditResult").setValue(type);
			if("1"==type){
				mini.get("shyj").setValue("同意");
			}else{
				mini.get("shyj").setValue("不同意");
			}
			var win = mini.get("win1");
			//显示审核window
			win.showAtPos("center", "middle");
		}
		//关闭
		  function hideWindow() {
		      var win = mini.get("win1");
		      win.hide();
		  }
		// 保存提交
		function saveAudit(e) {
			mini.get("auditRemark").setValue(mini.get("shyj").getValue());

			document.form1.action = "${ctx}/cn/com/sinosoft/rs/partjob/partJobSubmit.ac";
			document.form1.submit();
			waitClick();
		}
		
	</script>
</head>
<body>
	<div id="layout1" class="mini-layout"
		style="width: 100%; height: 100%;">
		<div title="south" region="south" showHeader="false" height="60px">
			<div class="mini-toolbar"
				style="text-align: center; padding: 6px; border: 0;">
				<div class="mini-toolbar"
					style="text-align: center; padding: 6px; border: 0;">
					<c:if test="${pageState ne 'view'}">
						<a class="mini-button mini-button-iconTop" iconCls="icon-save"
							onClick="save('0')" style="margin-right: 20px;">&nbsp;&nbsp;保存&nbsp;&nbsp;</a>
						<a class="mini-button mini-button-iconTop" iconCls="icon-ok"
							onClick="save('1')" style="margin-right: 20px;">&nbsp;&nbsp;提交&nbsp;&nbsp;</a>
					</c:if>
					<c:if test="${(parama eq '1' || parama eq '3' || parama eq '4')&& taskid ne null}">
						<a class="mini-button mini-button-iconTop" iconCls="icon-redo"
							style="margin-right: 20px;" onClick="toSubmit('1')">&nbsp;&nbsp;通过&nbsp;&nbsp;</a>
						<a class="mini-button mini-button-iconTop" iconCls="icon-undo"
							style="margin-right: 20px;" onClick="toSubmit('0')">&nbsp;&nbsp;驳回&nbsp;&nbsp;</a>
					</c:if>
					<c:if test="${(parama eq '2')&& taskid ne null}">
						<a class="mini-button mini-button-iconTop" iconCls="icon-ok"
							style="margin-right: 20px;" onclick="toSubmit('1')">&nbsp;&nbsp;已确认&nbsp;&nbsp;</a>
							<a class="mini-button mini-button-iconTop" iconCls="icon-undo"
							style="margin-right: 20px;" onClick="toSubmit('0')">&nbsp;&nbsp;驳回&nbsp;&nbsp;</a>
					</c:if>
					<a class="mini-button mini-button-iconTop" iconCls="icon-close"
						onClick="CloseWindow('cancel')">&nbsp;&nbsp;关闭&nbsp;&nbsp;</a>
				</div>
			</div>
		</div>
		<c:if test="${piId ne null }">
			<div title="工作流程,查看请单击此处" region="north" height="300"
				showCollapseButton="false" showHeader="false" iconCls="icon-goto"
				expanded="false">
				<%@include
					file="/WEB-INF/pages/cn/com/sinosoft/common/workflowHistory.jsp"%>
			</div>
		</c:if>
<div title="center" region="center">
		<div class="mini-fit">
	<iframe name="post_frame" id="post_frame" style="display: none;"></iframe>
	<form id="form1" name="form1" method="post" target="post_frame"
		enctype="multipart/form-data">
		<s:token></s:token>
		<div style="display: none;">
			<bspHtml:Hidden property="saveType"></bspHtml:Hidden>
			<bspHtml:Hidden property="piId"></bspHtml:Hidden>
			<bspHtml:Hidden property="taskid"></bspHtml:Hidden>
			<bspHtml:Hidden property="auditResult"></bspHtml:Hidden>
			<bspHtml:Hidden property="auditRemark"></bspHtml:Hidden>
			<bspHtml:Hidden property="parama"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.id"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.dataSource"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.dataModyTime"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.piId"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.proState"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.state"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.addZone"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.addOrg"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.addDep"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.addUser"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.addTime"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.modyZone"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.modyOrg"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.modyDep"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.modyUser"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.modyTime"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.shrUser"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.shrRemart"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.shrDate"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.rsbaUser"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.rsbaRemart"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.rsbaDate"></bspHtml:Hidden>
		</div>
	<center>
	<table class="tab-1" cellpadding="5" cellspacing="0" border="1" align="center">
		<COLGROUP>
			<col align="right" style="width:25%"/>
			<col align="left" style="width:25%"/>
			<col align="right" style="width:25%"/>
			<col align="left" style="width:25%"/>
		</COLGROUP>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;申请科室：
			</td>
			<td>
				<bspHtml:ComboBox property="result.applyDep"
					style="width:100%;" enabled="false" required="true"
					requiredErrorText="[用印申请处室]不可为空"  
					value="${pageState eq 'add'?SESSION_USERVIEW.departmentId:result.applyDep}"
					valueField="ID" textField="NAME" url="BASE_TAG_DEPARTMENT">
				</bspHtml:ComboBox>
			</td>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;申请人：
			</td>
			<td>
				<bspHtml:TextBox property="result.applyUser"
					style="width:100%;" 
					emptyText="请输入申请人" maxLength="50"
					vtype="rangeChar:0,50;" 
					value="${pageState eq 'add'? SESSION_USERVIEW.name : result.applyUser}"
					required="true" requiredErrorText="[申请人] 不能为空"
					rangeCharErrorText="[申请人] 字符数必须在 {0} 到 {1} 之间">
				</bspHtml:TextBox>
			</td>
			
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;担任职务：
			</td>
			<td>
				<bspHtml:TextBox property="result.holdPost"
					style="width:100%;" 
					emptyText="请输入担任职务" maxLength="50"
					vtype="rangeChar:0,50;"
					required="true" requiredErrorText="[担任职务] 不能为空"
					rangeCharErrorText="[担任职务] 字符数必须在 {0} 到 {1} 之间">
				</bspHtml:TextBox>
			</td>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;时间：
			</td>
			<td>
				<bspHtml:DatePicker property="result.applyTime"
					style="width:100%;" 
					format="yyyy-MM-dd" value="<%=Util.getToDay()%>" emptyText="请选择申请时间">
				</bspHtml:DatePicker>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;兼职单位名称及性质：
			</td>
			<td>
				<bspHtml:TextBox property="result.partJobUnit"
					style="width:100%;"
					emptyText="请输入兼职单位名称及性质" maxLength="500"
					required="true" requiredErrorText="[兼职单位名称及性质] 不能为空"
					vtype="rangeChar:0,500;"
					rangeCharErrorText="[兼职单位名称及性质] 字符数必须在 {0} 到 {1} 之间">
				</bspHtml:TextBox>
			</td>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;隶属关系及级别：
			</td>
			<td>
				<bspHtml:TextBox property="result.membership"
					style="width:100%;"
					emptyText="请输入隶属关系及级别" maxLength="500"
					required="true" requiredErrorText="[隶属关系及级别] 不能为空"
					vtype="rangeChar:0,500;"
					rangeCharErrorText="[隶属关系及级别] 字符数必须在 {0} 到 {1} 之间">
				</bspHtml:TextBox>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;现有兼职数量：
			</td>
			<td>
				<bspHtml:TextBox property="result.partJobNumber"
					style="width:100%;" 
					emptyText="请输入现有兼职数量" maxLength="50"
					vtype="rangeChar:0,50;" 
					required="true" requiredErrorText="[现有兼职数量] 不能为空"
					rangeCharErrorText="[现有兼职数量] 字符数必须在 {0} 到 {1} 之间">
				</bspHtml:TextBox>
			</td>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;是否为续聘：
			</td>
			<td>
				<bspHtml:RadioButtonList property="result.reEmploy"
					style="width:100%;"  
					url="RS_RE_EMPLOY" value="1" 
					required="true" requiredErrorText="[是否为续聘] 不能为空"
					textField="NAME" valueField="ID">
				</bspHtml:RadioButtonList>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;现有兼职情况：
			</td>
			<td colspan="3">
				<bspHtml:TextArea property="result.partJobExist"
					style="width:100%;height:100px;"
					emptyText="填写示例：中华预防医学会某某分会-委员-兼职时间" maxLength="2000"
					vtype="rangeChar:0,2000;"
					required="true" requiredErrorText="[现有兼职情况] 不能为空"
					rangeCharErrorText="[现有兼职情况] 字符数必须在 {0} 到 {1} 之间">
				</bspHtml:TextArea>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;申请理由：
			</td>
			<td colspan="3">
				<bspHtml:TextArea property="result.applyAccount"
					style="width:100%;height:100px;"
					emptyText="请输入申请理由" maxLength="2000"
					vtype="rangeChar:0,2000;"
					required="true" requiredErrorText="[申请理由] 不能为空"
					rangeCharErrorText="[申请理由] 字符数必须在 {0} 到 {1} 之间">
				</bspHtml:TextArea>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;单位地址/工作地点：
			</td>
			<td colspan="3">
				<bspHtml:TextArea property="result.unitAddress"
					style="width:100%;" 
					emptyText="请输入单位地址/工作地点" maxLength="200"
					required="true" requiredErrorText="[单位地址/工作地点] 不能为空"
					vtype="rangeChar:0,200;"
					rangeCharErrorText="[单位地址/工作地点] 字符数必须在 {0} 到 {1} 之间">
				</bspHtml:TextArea>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;是否领取薪酬：
			</td>
			<td>
				<bspHtml:RadioButtonList property="result.isReceiveSalary"
					style="width:100%;"  
					url="ICMIS_ISORNO" value="1"
					required="true" requiredErrorText="[是否领取薪酬] 不能为空"
					textField="NAME" valueField="ID">
				</bspHtml:RadioButtonList>
			</td>
			<c:if test="${sessionScope.SESSION_USERVIEW.position ne '10'}">
				<td align="right" class="bgcolor">
					<font color="red">*</font>&nbsp;发往所领导：
				</td>
				<td>
					<bspHtml:ComboBox property="result.leader"
						style="width:100%;"  
						url="CM_ZGSLDSP_USERNAME"
						allowInput="false"
						viewState="edit"
						emptyText="人事处选择分管所领导,可以直接提交" 
						textField="NAME" valueField="ID"
						requiredErrorText="[所领导] 不能为空">
					</bspHtml:ComboBox>
				</td>
			</c:if>
		</tr>
	</table>
	</center>
	</form>
	</div>
</div>
</div>
		<!-- 审批 -->
	<div id="win1" class="mini-window" title="审核"
		style="width: 600px; height: 300px; display: block; position: absolute; top: -999em;"
		showMaxButton="true" showCollapseButton="true" showShadow="true"
		showToolbar="false" showFooter="true" showModal="true"
		allowResize="true" allowDrag="true">
		<table class="tab-1" cellpadding="5" cellspacing="0" border="1"
			align="center">
			<colgroup>
				<col width="25%" />
				<col width="75%" />
			</colgroup>
			<tr>
				<td align="right" class="bgcolor"><font color="red">*</font>&nbsp;审核意见：</td>
				<td><bspHtml:TextArea property="shyj" viewState="edit"
						emptyText="请填写审核意见" vtype="maxLength:200" required="true"
						requiredErrorText="请填写审核意见" maxLength="200"
						style="width:100%;height:80px;" /></td>
			</tr> 
		</table>
		<div property="footer"
			style="text-align: center; height: 30px; padding-top: 5px;">
			<a class="mini-button" plain="false" iconCls="icon-ok"
				onclick="saveAudit">提交</a> &nbsp;&nbsp;&nbsp;&nbsp; <a
				class="mini-button" plain="false" iconCls="icon-close"
				onclick="hideWindow">关闭</a>
		</div>
	</div>
</body>
</html>						
						<td align="right"><nobr>&nbsp;申请时间：</nobr></td>
						<td>
							<bspHtml:DatePicker property="session_APPLY_TIME_BEGIN"
								format="yyyy-MM-dd"
								value="<%=Util.getMonthFirstDay()%>" style="width:120px">
							</bspHtml:DatePicker>
						</td>
						<td align="center"><nobr>&nbsp;至</nobr></td>
						<td colspan="1">
							<bspHtml:DatePicker property="session_APPLY_TIME_END"
								format="yyyy-MM-dd"
								value="<%=Util.getToDay()%>" style="width:120px">
							</bspHtml:DatePicker>
						</td>
						<td align="right"><nobr>&nbsp;审核状态：</nobr></td>
						<td>
							<bspHtml:ComboBox property="session_PROSTATE" valueField="ID" textField="NAME" style="width:100%"
							showNullItem="true" nullItemText="全部"  value="0,1,2,3" url="RS_PRO_STATE" multiSelect="true"></bspHtml:ComboBox>
						</td>
						<td align="center" >
							<a class="mini-button" iconCls="icon-search" onclick="search()">查询</a>
						</td>
						
					</tr>
				</table>
			</div>
		</div>
		<div title="<%=title%>" region="center" showHeader="false">
			<div class="mini-toolbar" style="padding: 2px;" borderStyle="border-left:0;border-top:0;border-right:0;">
				<perm:permission funcID="<%=viewFunc %>">
					<script>perm = true;</script>
					<a class="mini-button" iconCls="icon-node" plain="true" onclick="view()">查看</a>
				</perm:permission>
				<perm:permission funcID="<%=addFunc %>">
					<a class="mini-button" iconCls="icon-add" plain="true" onclick="add()">添加</a>
				</perm:permission>
				<perm:permission funcID="<%=edtFunc %>">
					<a class="mini-button" iconCls="icon-edit" plain="true" onclick="edit()">修改</a>
				</perm:permission>
				<perm:permission funcID="<%=delFunc %>">
					<a class="mini-button" iconCls="icon-remove" plain="true" onclick="remove()">删除</a>
				</perm:permission>
				<perm:permission funcID="<%=expFunc %>">
					<a class="mini-button" iconCls="icon-download" plain="true" onclick="exportExcel(grid)">导出Excel</a>
				</perm:permission>
				<perm:permission funcID="<%=submitFunc %>">
					<a class="mini-button" iconCls="icon-ok" plain="true" onclick="submitApply()">提交</a>
				</perm:permission>
				<perm:permission funcID="<%=revokeFunc %>">
					<a class="mini-button" iconCls="icon-no" plain="true" onclick="revokeApply()">撤销申请</a>
				</perm:permission>
				<perm:permission funcID="<%=expFunc %>">
					<a class="mini-button" iconCls="icon-download" plain="true" onclick="exportExcel(grid)">导出Excel</a>
				</perm:permission>
			</div>
			<!--撑满页面-->
			<div class="mini-fit">
				<div id="datagrid1" class="mini-datagrid" idField="ID" sortMode="client"
					 allowAlternating="true" url="${ctx }/cn/com/sinosoft/rs/partjob/qryPartJobList.ac"
					 style="width: 100%; height: 100%;" sizeList="[20,50,100]" pageSize="20"
					 multiSelect="true" borderStyle="border:0" selectOnLoad="true">
					<div property="columns">
						<div type="checkcolumn" align="center" headerAlign="center" width="50"></div>
						<div type="indexcolumn" align="center" headerAlign="center" width="50">序号</div>
						<div header="申请人" field="APPLY_USER" headerAlign="center">
						</div>
						<div header="申请科室" field="APPLY_DEP_NAME" headerAlign="center">
						</div>
						<div header="申请时间" field="APPLY_TIME" headerAlign="center" dateFormat="yyyy-MM-dd"
							 dataType="date" align="center" width="150">
						</div>
						<!--<div header="单位地址/工作地点" field="UNIT_ADDRESS" headerAlign="center">
						</div>-->
						<div header="兼职单位名称及性质" field="PART_JOB_UNIT" headerAlign="center">
						</div>
						<div header="担任职务" field="HOLD_POST" headerAlign="center">
						</div>
						<div header="流程状态" field="PRO_STATE_NAME" headerAlign="center">
						</div>
						<div header="添加时间" field="ADD_TIME" headerAlign="center" dateFormat="yyyy-MM-dd HH:mm:ss"
							 dataType="date" align="center" width="150">
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</body>
</html>