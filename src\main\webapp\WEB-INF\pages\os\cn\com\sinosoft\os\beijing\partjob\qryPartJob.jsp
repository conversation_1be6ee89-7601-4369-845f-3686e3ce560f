<%@page import="cn.com.sinosoft.constant.RsConstant"%>
<%@ page language="java" contentType="text/html; charset=GBK" pageEncoding="GBK"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<jsp:directive.page import="ie.weaf.toolkit.Util" />
	<%@ include file="/common/taglibs.jsp"%>
<%
	String title = "��ְ����"; // ����
	String viewFunc = "OS000020201"; // �鿴Ȩ��
	String addFunc = "OS000020202"; // ����Ȩ��
	String edtFunc = "OS000020203"; // �޸�Ȩ��
	String delFunc = "OS000020204"; // ɾ��Ȩ��
	String submitFunc = "OS000020205"; // �ύȨ��
	String revokeFunc = "OS000020206"; //��������Ȩ��
	String expFunc = "OS000020207"; // ����Ȩ��
	String kyFucn = "OS0000801";//��Ա��ѯȨ��
	String kzFucn = "OS0000802";//�Ƴ���ѯȨ��
	String allFucn = "OS0000803";//������ѯȨ��
	String isAllFunc = "";
	String isKzFucn = "";
	String isKyFucn = "";
	String opers =  taglibs_userview.getOpers();
	if(!Util.isNullOrEmpty(opers)){
		String arrsOpers[] = opers.split(",");
		for(int i=0;i<arrsOpers.length;i++){
			if(allFucn.equals(arrsOpers[i]) ){
				isAllFunc = "1";
			}
			if(kzFucn.equals(arrsOpers[i])){
				isKzFucn="1";
			}
			if(kyFucn.equals(arrsOpers[i])){
				isKyFucn="1";
			}
		}
	} 
%>
<html>
<head>
	<title><%=title%></title>
	<meta http-equiv="content-type" content="text/html; charset=GBK" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<c:set value="<%=isAllFunc %>" var="isAllFunc"></c:set>
	<c:set value="<%=isKzFucn %>" var="isKzFucn"></c:set>
	<c:set value="<%=isKyFucn %>" var="isKyFucn"></c:set>
	<style type="text/css">
	.myrow {
		 color: gray;
	}
	</style>
	<script type="text/javascript">
		var grid,form,perm = false;
		// ��ʼ������
		$(document).ready(function() {
			mini.parse();
			
			var n = mini.get("layout1").getRegion("north");
			if(n){
				n.height=$("#qryTable").height()+55;
				mini.layout();
				form = new mini.Form("form1");
			}
			grid = mini.get("datagrid1");
			if (perm) {
				// ˫��Grid��ִ�в鿴 
				grid.on("rowdblclick", function(e) {
					view();
				});
			}
			grid.on("drawcell", function (e) {
				var record = e.record;
				//��������ʽ
				if (record.STATE_CODE == '0') {
					e.rowCls = "myrow";
				}
			});
			search();
		});


		// ��ѯ����
		function search() {
			if(form){
				form.validate();
				if (form.isValid() == false){
					showFormErrorTexts(form.getErrorTexts(),400,300);
					return;
				}
			}
			var ky='',kz='';
			 if('1'=='${isKzFucn}'){
				kz='${SESSION_USERVIEW.departmentId}'
			}else if('1'=='${isKyFucn}'){
				ky='${SESSION_USERVIEW.username}'
			}
			
			grid.load({
				session_KY :encodeURIComponent(ky),
				session_KZ :encodeURIComponent(kz), 
				session_PROSTATE :encodeURIComponent(mini.get("session_PROSTATE").getValue()), //���״̬
				session_PRO_USER :encodeURIComponent('${SESSION_USERVIEW.username}'),
				session_APPLY_USER : encodeURIComponent(mini.get("session_APPLY_USER").getValue()), // ������
				session_APPLY_DEP : encodeURIComponent(mini.get("session_APPLY_DEP").getValue()), // �������
				session_APPLY_TIME_BEGIN : encodeURIComponent(mini.get("session_APPLY_TIME_BEGIN").getFormValue()), // ����ʱ��
				session_APPLY_TIME_END : encodeURIComponent(mini.get("session_APPLY_TIME_END").getFormValue()) // ��
			},function(){
				//���سɹ�֮��Ҫ��������
			});
		}

		// �鿴
		function view() {
			var records = grid.getSelecteds();
			if (records.length > 1) {
				mini.alert('���ɲ鿴������¼��');
				return;
			}
			var record = records[0];
			if (record) {
				var win = mini.open({
					url : "${ctx}/cn/com/sinosoft/os/beijing/partjob/partJob_viewParent.ac?pageState=view&id="
						+ record.ID,
					title : "�鿴",
					width : 800,
					height : 400,
					showMaxButton : true,
					ondestroy : function(action) {
						// ���ڹر�ʱִ��
					}
				});
			 win.max();
			} else {
				mini.alert("��ѡ��һ����¼");
			}
		}

		// ����
		function add() {
			var win = mini.open({
				url : "${ctx}/cn/com/sinosoft/os/beijing/partjob/partJob_addParentInput.ac?pageState=add",
				title : "����",
				width : 800,
				height : 400,
				showMaxButton : true,
				ondestroy : function(action) {
					// ���ڹر�ʱִ��
					if (action == "save"){
						grid.reload();
					}
				}
			});
			 win.max();
		}

		// �޸�
		function edit() {
			var records = grid.getSelecteds();
			if (records.length > 1) {
				mini.alert('�����޸Ķ�����¼��');
				return;
			}
			var record = records[0];
			if (record) {
				// �޸�Ȩ�޿���
				if('<%=RsConstant.PRO_STATE_SAVE%>' !=record.PRO_STATE){
					 mini.alert("ֻ�����״̬Ϊ���ѱ��桿�����ݣ�");
					 return;
				 }
				if("${SESSION_USERVIEW.username}" != record.ADD_USER){
					mini.alert("ֻ���޸ĵ�ǰ�û����������");
					 return;
				}
				//���޸Ĵ���
				var win = mini.open({
					url : "${ctx}/cn/com/sinosoft/os/beijing/partjob/partJob_edtParentInput.ac?pageState=edit&id="
						+ record.ID,
					title : "�޸�",
					width : 800,
					height : 400,
					showMaxButton : true,
					ondestroy : function(action) {
						// ���ڹر�ʱִ��
						if (action == "save"){
							grid.reload();
						}
					}
				});
				 win.max();
			} else {
				mini.alert("��ѡ��һ����¼");
			}
		}

		// ɾ��
		function remove() {
			var rows = grid.getSelecteds();
			if (rows.length > 0) {
				// ɾ��Ȩ�޿���
				// ��ɾ��Ȩ�޿���
				// ɾ��ȷ��
				// �����״̬'<%=RsConstant.PRO_STATE_SAVE%>' !=rows[i].PRO_STATE 
				mini.confirm ("ȷ��ɾ��ѡ�е�" + rows.length + "����¼��","ɾ����¼",
					function(action) {
						if (action == "ok") {
							var id = [];
							for (var i = 0, l = rows.length; i < l; i++) {
								if("${SESSION_USERVIEW.username}" != rows[i].ADD_USER){
									mini.alert("ֻ��ɾ����ǰ�û����������");
									 return;
								}
								if (rows[i].PRO_STATE_NAME != "�ѱ���" && rows[i].PRO_STATE_NAME != "�����˶���") {
									mini.alert("ֻ��ɾ���������˶����������ݣ�");
									 return;
								}
								id.push(rows[i].ID);
							}
							id = id.join(',');
							//ִ��ɾ��
							grid.loading("ɾ���У����Ժ�......");
							$.ajax({
								url : "${ctx}/cn/com/sinosoft/os/beijing/partjob/partJob_delParentSubmit.ac?id="
									+ id,
								success : function() {
									grid.reload();
								},
								error : function(XMLHttpRequest, textStatus, errorThrown) {

								}
							});
						}
					}
				);
			} else {
				mini.alert("��ѡ��Ҫɾ���ļ�¼");
			}
		}
		//�ύ
		function submitApply(){
			var rows = grid.getSelecteds();
			if (rows.length > 0) {
				 
				mini.confirm ("ȷ���ύѡ�е�" + rows.length + "����¼��","�ύ��¼",
					function(action) {
						if (action == "ok") {
							var id = [];
							for (var i = 0, l = rows.length; i < l; i++) {
								if('<%=RsConstant.PRO_STATE_SAVE%>' !=rows[i].PRO_STATE){
									 mini.alert("ֻ���ύ״̬Ϊ���ѱ��桿�����ݣ�");
									 return;
								 }
								if("${SESSION_USERVIEW.username}" != rows[i].ADD_USER){
									mini.alert("ֻ���ύ��ǰ�û����������");
									 return;
								}
								id.push(rows[i].ID);
							}
							id = id.join(',');
							//ִ��ɾ��
							grid.loading("�ύ�У����Ժ�......");
							$.ajax({
								url : "${ctx}/cn/com/sinosoft/os/beijing/partjob/partJob_submitApply.ac?id="
									+ id,
								success : function() {
									grid.reload();
								},
								error : function(XMLHttpRequest, textStatus, errorThrown) {

								}
							});
						}
					}
				);
			} else {
				mini.alert("��ѡ��Ҫɾ���ļ�¼");
			}
		}
		//��������
		function revokeApply(){
			var rows = grid.getSelecteds();
			if (rows.length > 0) {
				 
				mini.confirm ("ȷ������ѡ�е�" + rows.length + "�������¼��","���������¼",
					function(action) {
						if (action == "ok") {
							var id = [];
							for (var i = 0, l = rows.length; i < l; i++) {
								if('<%=RsConstant.PRO_STATE_SUBMIT%>' !=rows[i].PRO_STATE){
									 mini.alert("ֻ�ܳ���״̬Ϊ�����ύ���������¼��");
									 return;
								 }
								if("${SESSION_USERVIEW.username}" != rows[i].ADD_USER){
									mini.alert("ֻ�ܳ�����ǰ�û����������");
									 return;
								}
								id.push(rows[i].ID);
							}
							id = id.join(',');
							//ִ��ɾ��
							grid.loading("�����У����Ժ�......");
							$.ajax({
								url : "${ctx}/cn/com/sinosoft/os/beijing/partjob/partJob_revokeApply.ac?id="
									+ id,
								success : function() {
									grid.reload();
								},
								error : function(XMLHttpRequest, textStatus, errorThrown) {

								}
							});
						}
					}
				);
			} else {
				mini.alert("��ѡ��Ҫɾ���ļ�¼");
			}
		}
	</script>
</head>
<body>
	<!-- ����Excel���HTML start -->
	<form id="excelForm" enctype="multipart/form-data" 
		action="${ctx }/util/util_exportExcle.ac" method="post" 
		target="excelIFrame" style="display:none;">
		<!-- ��������Ϣ -->
		<input type="hidden" name="headData" id="headData" />
		<!-- �����б����ݣ������Ŀǰ�ѷ����� -->
		<input type="hidden" name="bodyData" id="bodyData" />
		<!-- ����������ʾ���ֶ�field�����ŷָ� -->
		<input type="hidden" name="export_hideColumn" id="export_hideColumn"  />
		<!-- Ҫ�������������ֶ�field�����ŷָ� -->
		<input type="hidden" name="export_showColumn" id="export_showColumn" />
	</form>
	<iframe id="excelIFrame" name="excelIFrame" style="display:none;"></iframe>
	<!-- ����Excel���HTML end -->
	<div id="layout1" class="mini-layout" style="width: 100%; height: 100%;">
		<div title="<%=title%>" region="north" height="72" showHeader="true" class="util_search" >
			<div id="form1" style="padding: 0;margin: 0;">
				<table id="qryTable" align="center" style="margin-top: 6px;" cellpadding="2">
					<tr>
						<td align="right"><nobr>&nbsp;������ң�</nobr></td>
						<td>
							<bspHtml:ComboBox property="session_APPLY_DEP"  style="width:100%;"
								 valueField="ID" textField="NAME" url="BASE_TAG_DEPARTMENT"
								 value="${isAllFunc=='1'?'':SESSION_USERVIEW.departmentId}" 
								 enabled="${isAllFunc=='1'?true:false}"
								 >
							</bspHtml:ComboBox>
						</td>
						<td align="right"><nobr>&nbsp;�����ˣ�</nobr></td>
						<td>
							<bspHtml:TextBox property="session_APPLY_USER"
								onenter="search()" vtype="maxLength:38;rangeChar:0,4000;" maxLength="4000"
								maxLengthErrorText="[������] ���ܳ��� {0} ���ַ�"
								rangeCharErrorText="[������] �ַ��������� {0} �� {1} ֮��" style="width:100%">
							</bspHtml:TextBox>
						</td>
						
						<td align="right"><nobr>&nbsp;����ʱ�䣺</nobr></td>
						<td>
							<bspHtml:DatePicker property="session_APPLY_TIME_BEGIN"
								format="yyyy-MM-dd"
								value="<%=Util.getMonthFirstDay()%>" style="width:120px">
							</bspHtml:DatePicker>
						</td>
						<td align="center"><nobr>&nbsp;��</nobr></td>
						<td colspan="1">
							<bspHtml:DatePicker property="session_APPLY_TIME_END"
								format="yyyy-MM-dd"
								value="<%=Util.getToDay()%>" style="width:120px">
							</bspHtml:DatePicker>
						</td>
						<td align="right"><nobr>&nbsp;���״̬��</nobr></td>
						<td>
							<bspHtml:ComboBox property="session_PROSTATE" valueField="ID" textField="NAME" style="width:100%"
							showNullItem="true" nullItemText="ȫ��"  value="0,1,2,3" url="RS_PRO_STATE" multiSelect="true"></bspHtml:ComboBox>
						</td>
						<td align="center" >
							<a class="mini-button" iconCls="icon-search" onclick="search()">��ѯ</a>
						</td>
						
					</tr>
				</table>
			</div>
		</div>
		<div title="<%=title%>" region="center" showHeader="false">
			<div class="mini-toolbar" style="padding: 2px;" borderStyle="border-left:0;border-top:0;border-right:0;">
				<perm:permission funcID="<%=viewFunc %>">
					<script>perm = true;</script>
					<a class="mini-button" iconCls="icon-node" plain="true" onclick="view()">�鿴</a>
				</perm:permission>
				<perm:permission funcID="<%=addFunc %>">
					<a class="mini-button" iconCls="icon-add" plain="true" onclick="add()">����</a>
				</perm:permission>
				<perm:permission funcID="<%=edtFunc %>">
					<a class="mini-button" iconCls="icon-edit" plain="true" onclick="edit()">�޸�</a>
				</perm:permission>
				<perm:permission funcID="<%=delFunc %>">
					<a class="mini-button" iconCls="icon-remove" plain="true" onclick="remove()">ɾ��</a>
				</perm:permission>
				<perm:permission funcID="<%=expFunc %>">
					<a class="mini-button" iconCls="icon-download" plain="true" onclick="exportExcel(grid)">����Excel</a>
				</perm:permission>
				<perm:permission funcID="<%=submitFunc %>">
					<a class="mini-button" iconCls="icon-ok" plain="true" onclick="submitApply()">�ύ</a>
				</perm:permission>
				<perm:permission funcID="<%=revokeFunc %>">
					<a class="mini-button" iconCls="icon-no" plain="true" onclick="revokeApply()">��������</a>
				</perm:permission>
				<perm:permission funcID="<%=expFunc %>">
					<a class="mini-button" iconCls="icon-download" plain="true" onclick="exportExcel(grid)">����Excel</a>
				</perm:permission>
			</div>
			<!--����ҳ��-->
			<div class="mini-fit">
				<div id="datagrid1" class="mini-datagrid" idField="ID" sortMode="client"
					 allowAlternating="true" url="${ctx }/cn/com/sinosoft/os/beijing/partjob/qryPartJobList.ac"
					 style="width: 100%; height: 100%;" sizeList="[20,50,100]" pageSize="20"
					 multiSelect="true" borderStyle="border:0" selectOnLoad="true">
					<div property="columns">
						<div type="checkcolumn" align="center" headerAlign="center" width="50"></div>
						<div type="indexcolumn" align="center" headerAlign="center" width="50">���</div>
						<div header="������" field="APPLY_USER" headerAlign="center">
						</div>
						<div header="�������" field="APPLY_DEP_NAME" headerAlign="center">
						</div>
						<div header="����ʱ��" field="APPLY_TIME" headerAlign="center" dateFormat="yyyy-MM-dd"
							 dataType="date" align="center" width="150">
						</div>
						<!--<div header="��λ��ַ/�����ص�" field="UNIT_ADDRESS" headerAlign="center">
						</div>-->
						<div header="��ְ��λ���Ƽ�����" field="PART_JOB_UNIT" headerAlign="center">
						</div>
						<div header="����ְ��" field="HOLD_POST" headerAlign="center">
						</div>
						<div header="����״̬" field="PRO_STATE_NAME" headerAlign="center">
						</div>
						<div header="����ʱ��" field="ADD_TIME" headerAlign="center" dateFormat="yyyy-MM-dd HH:mm:ss"
							 dataType="date" align="center" width="150">
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</body>
</html>