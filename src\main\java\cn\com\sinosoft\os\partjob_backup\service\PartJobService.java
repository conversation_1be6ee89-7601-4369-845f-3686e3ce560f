package cn.com.sinosoft.rs.partjob.service;

import cn.com.sinosoft.rs.certificateuse.model.CertificateUse;
import cn.com.sinosoft.rs.partjob.model.PartJob;
import java.util.List;

/**
 * 兼职管理 - service接口.
 *
 * <AUTHOR>
 * @date: 2023/12/21 11:09:09
 * @version V1.0
 */
public interface PartJobService {

	/**
	 * 根据id获取兼职信息
	 * 
	 * @param id
	 * @return
	 */
	public PartJob get(String id);

	/**
	 * 删除兼职信息
	 * 
	 * @param ids
	 */
	public void delete(String ids);

	/**
	 * 保存兼职信息
	 * 
	 * @param result
	 */
	public void save(PartJob result);

	/**
	 * 修改兼职信息
	 * 
	 * @param result
	 */
	public void edit(PartJob result);

	/**
	 * 根据流程实例id获取兼职信息
	 * 
	 * @param piId
	 * @return
	 */
	public PartJob getPiId(String piId);

	/**
	 * 提交申请
	 * 
	 * @param result
	 * @param parama
	 * @param taskid
	 * @param piId
	 */
	public void applySubmit(PartJob result, String parama, String taskid, String piId);

	/**
	 * 撤销申请
	 * 
	 * @param id
	 */
	public void revokeApply(String id);

	/**
	 * 提交申请
	 * 
	 * @param id
	 */
	public void submitApply(String id);
}