package cn.com.sinosoft.os.beijingexitapply.action;

import ie.bsp.frame.action.BaseEditAction;
import ie.bsp.frame.exception.GeneralException;
import cn.com.sinosoft.os.beijingexitapply.model.BeijingExitApply;
import cn.com.sinosoft.os.beijingexitapply.service.BeijingExitApplyService;
import ie.weaf.toolkit.Util;
import ie.bsp.frame.exception.GeneralExceptionHandler;

/**
 * 出京申请 - Action.
 *
 * <AUTHOR>
 * @date: 2025/08/02 15:41:43
 * @version V1.0
 */
public class BeijingExitApplyAction extends BaseEditAction {

	/**
	 * 默认构造.
	 */
	public BeijingExitApplyAction() {
		moduleId = "";
	}

	// serialVersionUID.
	private static final long serialVersionUID = 1L;

	// 出京申请 - 接口.
	private BeijingExitApplyService service;

	// 出京申请 - 对象.
	private BeijingExitApply result;

	// 主键.
	private String id;

	/**
	 * 获取 出京申请 - 接口.
	 *
	 * @return 出京申请 - 接口
	 */
	public BeijingExitApplyService getService() {
		return service;
	}

	/**
	 * 设置 出京申请 - 接口.
	 *
	 * @param service
	 *			     出京申请 - 接口
	 */
	public void setService(BeijingExitApplyService service) {
		this.service = service;
	}

	/**
	 * 获取 出京申请 - 对象.
	 *
	 * @return 出京申请 - 对象
	 */
	public BeijingExitApply getResult() {
		return result;
	}

	/**
	 * 设置 出京申请 - 对象.
	 *
	 * @param result
	 *			     出京申请 - 对象
	 */
	public void setResult(BeijingExitApply result) {
		this.result = result;
	}

	/**
	 * 获取 主键.
	 *
	 * @return 主键
	 */
	public String getId() {
		return id;
	}

	/**
	 * 设置 主键.
	 *
	 * @param id
	 *			     主键
	 */
	public void setId(String id) {
		this.id = id;
	}

	@Override
	public void doQryParentInput() throws GeneralException {
		
	}

	@Override
	public void doViewParentSubmit() throws GeneralException {
		funcId = "";
		result = service.get(id);
	}

	@Override
	public void doDelteParentSubmit() throws GeneralException {
		funcId = "";
		service.delete(id);
	}

	@Override
	public void doAddParentInput() throws GeneralException {
		
	}

	@Override
	public void doAddParentSubmit() throws GeneralException {
		funcId = "";
		service.save(result);
	}

	@Override
	public void doEditParentInput() throws GeneralException {
		doViewParentSubmit();
	}

	@Override
	public void doEditParentSubmit() throws GeneralException {
		funcId = "";
		service.edit(result);
	}

	@Override
	public void doAudit() throws GeneralException {
		funcId = "";
	}

}	 

	public Date getRsbaDate() {
		return rsbaDate;
	}

	public void setRsbaDate(Date rsbaDate) {
		this.rsbaDate = rsbaDate;
	}

	public String getShrUser() {
		return shrUser;
	}

	public void setShrUser(String shrUser) {
		this.shrUser = shrUser;
	}


	public String getShrRemart() {
		return shrRemart;
	}

	public void setShrRemart(String shrRemart) {
		this.shrRemart = shrRemart;
	}

	public Date getShrDate() {
		return shrDate;
	}

	public void setShrDate(Date shrDate) {
		this.shrDate = shrDate;
	}

	/**
	 * 获取 主键.
	 *
	 * @return 主键
	 */
	public String getId() {
		return id;
	}

	/**
	 * 设置 主键.
	 *
	 * @param id
	 *			     主键
	 */
	public void setId(String id) {
		this.id = id;
	}

	/**
	 * 获取 申请人.
	 *
	 * @return 申请人
	 */
	public String getApplyUser() {
		return applyUser;
	}

	/**
	 * 设置 申请人.
	 *
	 * @param applyUser
	 *			     申请人
	 */
	public void setApplyUser(String applyUser) {
		this.applyUser = applyUser;
	}

	/**
	 * 获取 申请科室.
	 *
	 * @return 申请科室
	 */
	public String getApplyDep() {
		return applyDep;
	}

	/**
	 * 设置 申请科室.
	 *
	 * @param applyDep
	 *			     申请科室
	 */
	public void setApplyDep(String applyDep) {
		this.applyDep = applyDep;
	}

	/**
	 * 获取 申请时间.
	 *
	 * @return 申请时间
	 */
	public Date getApplyTime() {
		return applyTime;
	}

	/**
	 * 设置 申请时间.
	 *
	 * @param applyTime
	 *			     申请时间
	 */
	public void setApplyTime(Date applyTime) {
		this.applyTime = applyTime;
	}

	/**
	 * 获取 申请账号.
	 *
	 * @return 申请账号
	 */
	public String getApplyAccount() {
		return applyAccount;
	}

	/**
	 * 设置 申请账号.
	 *
	 * @param applyAccount
	 *			     申请账号
	 */
	public void setApplyAccount(String applyAccount) {
		this.applyAccount = applyAccount;
	}

	/**
	 * 获取 兼职单位名称及地址.
	 *
	 * @return 兼职单位名称及地址
	 */
	public String getPartJobUnit() {
		return partJobUnit;
	}

	/**
	 * 设置 兼职单位名称及地址.
	 *
	 * @param partJobUnit
	 *			     兼职单位名称及地址
	 */
	public void setPartJobUnit(String partJobUnit) {
		this.partJobUnit = partJobUnit;
	}

	/**
	 * 获取 党团关系所在地.
	 *
	 * @return 党团关系所在地
	 */
	public String getMembership() {
		return membership;
	}

	/**
	 * 设置 党团关系所在地.
	 *
	 * @param membership
	 *			     党团关系所在地
	 */
	public void setMembership(String membership) {
		this.membership = membership;
	}

	/**
	 * 获取 单位地址/工作地点.
	 *
	 * @return 单位地址/工作地点
	 */
	public String getUnitAddress() {
		return unitAddress;
	}

	/**
	 * 设置 单位地址/工作地点.
	 *
	 * @param unitAddress
	 *			     单位地址/工作地点
	 */
	public void setUnitAddress(String unitAddress) {
		this.unitAddress = unitAddress;
	}

	/**
	 * 获取 担任职务.
	 *
	 * @return 担任职务
	 */
	public String getHoldPost() {
		return holdPost;
	}

	/**
	 * 设置 担任职务.
	 *
	 * @param holdPost
	 *			     担任职务
	 */
	public void setHoldPost(String holdPost) {
		this.holdPost = holdPost;
	}

	/**
	 * 获取 是否领取薪酬.
	 *
	 * @return 是否领取薪酬
	 */
	public String getIsReceiveSalary() {
		return isReceiveSalary;
	}

	/**
	 * 设置 是否领取薪酬.
	 *
	 * @param isReceiveSalary
	 *			     是否领取薪酬
	 */
	public void setIsReceiveSalary(String isReceiveSalary) {
		this.isReceiveSalary = isReceiveSalary;
	}

	/**
	 * 获取 流程标识.
	 *
	 * @return 流程标识
	 */
	public String getPiId() {
		return piId;
	}

	/**
	 * 设置 流程标识.
	 *
	 * @param piId
	 *			     流程标识
	 */
	public void setPiId(String piId) {
		this.piId = piId;
	}

	/**
	 * 获取 流程状态.
	 *
	 * @return 流程状态
	 */
	public String getProState() {
		return proState;
	}

	/**
	 * 设置 流程状态.
	 *
	 * @param proState
	 *			     流程状态
	 */
	public void setProState(String proState) {
		this.proState = proState;
	}

	/**
	 * 获取 添加地区.
	 *
	 * @return 添加地区
	 */
	public String getAddZone() {
		return addZone;
	}

	/**
	 * 设置 添加地区.
	 *
	 * @param addZone
	 *			     添加地区
	 */
	public void setAddZone(String addZone) {
		this.addZone = addZone;
	}

	/**
	 * 获取 添加机构.
	 *
	 * @return 添加机构
	 */
	public String getAddOrg() {
		return addOrg;
	}

	/**
	 * 设置 添加机构.
	 *
	 * @param addOrg
	 *			     添加机构
	 */
	public void setAddOrg(String addOrg) {
		this.addOrg = addOrg;
	}

	/**
	 * 获取 添加科室.
	 *
	 * @return 添加科室
	 */
	public String getAddDep() {
		return addDep;
	}

	/**
	 * 设置 添加科室.
	 *
	 * @param addDep
	 *			     添加科室
	 */
	public void setAddDep(String addDep) {
		this.addDep = addDep;
	}

	/**
	 * 获取 添加人.
	 *
	 * @return 添加人
	 */
	public String getAddUser() {
		return addUser;
	}

	/**
	 * 设置 添加人.
	 *
	 * @param addUser
	 *			     添加人
	 */
	public void setAddUser(String addUser) {
		this.addUser = addUser;
	}

	/**
	 * 获取 添加时间.
	 *
	 * @return 添加时间
	 */
	public Date getAddTime() {
		return addTime;
	}

	/**
	 * 设置 添加时间.
	 *
	 * @param addTime
	 *			     添加时间
	 */
	public void setAddTime(Date addTime) {
		this.addTime = addTime;
	}

	/**
	 * 获取 修改地区.
	 *
	 * @return 修改地区
	 */
	public String getModyZone() {
		return modyZone;
	}

	/**
	 * 设置 修改地区.
	 *
	 * @param modyZone
	 *			     修改地区
	 */
	public void setModyZone(String modyZone) {
		this.modyZone = modyZone;
	}

	/**
	 * 获取 修改机构.
	 *
	 * @return 修改机构
	 */
	public String getModyOrg() {
		return modyOrg;
	}

	/**
	 * 设置 修改机构.
	 *
	 * @param modyOrg
	 *			     修改机构
	 */
	public void setModyOrg(String modyOrg) {
		this.modyOrg = modyOrg;
	}

	/**
	 * 获取 修改科室.
	 *
	 * @return 修改科室
	 */
	public String getModyDep() {
		return modyDep;
	}

	/**
	 * 设置 修改科室.
	 *
	 * @param modyDep
	 *			     修改科室
	 */
	public void setModyDep(String modyDep) {
		this.modyDep = modyDep;
	}

	/**
	 * 获取 修改人.
	 *
	 * @return 修改人
	 */
	public String getModyUser() {
		return modyUser;
	}

	/**
	 * 设置 修改人.
	 *
	 * @param modyUser
	 *			     修改人
	 */
	public void setModyUser(String modyUser) {
		this.modyUser = modyUser;
	}

	/**
	 * 获取 修改时间.
	 *
	 * @return 修改时间
	 */
	public Date getModyTime() {
		return modyTime;
	}

	/**
	 * 设置 修改时间.
	 *
	 * @param modyTime
	 *			     修改时间
	 */
	public void setModyTime(Date modyTime) {
		this.modyTime = modyTime;
	}

	/**
	 * 获取 是否有效.
	 *
	 * @return 是否有效
	 */
	public String getState() {
		return state;
	}

	/**
	 * 设置 是否有效.
	 *
	 * @param state
	 *			     是否有效
	 */
	public void setState(String state) {
		this.state = state;
	}

	/**
	 * 获取 数据接口-数据来源.
	 *
	 * @return 数据接口-数据来源
	 */
	public String getDataSource() {
		return dataSource;
	}

	/**
	 * 设置 数据接口-数据来源.
	 *
	 * @param dataSource
	 *			     数据接口-数据来源
	 */
	public void setDataSource(String dataSource) {
		this.dataSource = dataSource;
	}

	/**
	 * 获取 数据接口-修改时间.
	 *
	 * @return 数据接口-修改时间
	 */
	public Date getDataModyTime() {
		return dataModyTime;
	}

	/**
	 * 设置 数据接口-修改时间.
	 *
	 * @param dataModyTime
	 *			     数据接口-修改时间
	 */
	public void setDataModyTime(Date dataModyTime) {
		this.dataModyTime = dataModyTime;
	}

	public String getLeader() {
		return leader;
	}

	public void setLeader(String leader) {
		this.leader = leader;
	}

	public String getPartJobNumber() {
		return partJobNumber;
	}

	public void setPartJobNumber(String partJobNumber) {
		this.partJobNumber = partJobNumber;
	}

	public String getPartJobExist() {
		return partJobExist;
	}

	public void setPartJobExist(String partJobExist) {
		this.partJobExist = partJobExist;
	}

	public String getReEmploy() {
		return reEmploy;
	}

	public void setReEmploy(String reEmploy) {
		this.reEmploy = reEmploy;
	}

}
