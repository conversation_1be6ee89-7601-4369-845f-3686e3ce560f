锘?%@ page language="java" contentType="text/html; charset=GBK" pageEncoding="GBK"%>
<%@ page import="ie.weaf.toolkit.Util"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
	<title>鍏艰亴鐢宠 - 缁存姢</title>
	<meta http-equiv="content-type" content="text/html; charset=GBK" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<%@ include file="/common/taglibs.jsp"%>
	<script type="text/javascript">
		var form;
		$(document).ready(function() {
			mini.parse();
			form = new mini.Form("form1");
			$("#znksTd1").show();
		});
		//淇濆瓨
		function save(e) {
			form.validate();
			if (form.isValid() == false) {
				//鎻愮ず楠岃瘉閿欒淇℃伅锛?00涓烘彁绀烘瀹藉害锛?00涓烘彁绀烘楂樺害
				showFormErrorTexts(form.getErrorTexts(),400,300);
				form.getErrors()[0].focus();
				return;
			}
			mini.get("saveType").setValue(e);//鎻愪氦鏂瑰紡
			if ("${pageState}" == "add") {
				document.form1.action = "${ctx}/cn/com/sinosoft/rs/partjob/partJob_addParentSubmit.ac";
			} else {
				document.form1.action = "${ctx}/cn/com/sinosoft/rs/partjob/partJob_edtParentSubmit.ac";
			}
			document.form1.submit();
			waitClick();
		}
		
		//鎵撳紑瀹℃牳绐楀彛
		function toSubmit(type){
			mini.get("auditResult").setValue(type);
			if("1"==type){
				mini.get("shyj").setValue("鍚屾剰");
			}else{
				mini.get("shyj").setValue("涓嶅悓鎰?);
			}
			var win = mini.get("win1");
			//鏄剧ず瀹℃牳window
			win.showAtPos("center", "middle");
		}
		//鍏抽棴
		  function hideWindow() {
		      var win = mini.get("win1");
		      win.hide();
		  }
		// 淇濆瓨鎻愪氦
		function saveAudit(e) {
			mini.get("auditRemark").setValue(mini.get("shyj").getValue());

			document.form1.action = "${ctx}/cn/com/sinosoft/rs/partjob/partJobSubmit.ac";
			document.form1.submit();
			waitClick();
		}
		
	</script>
</head>
<body>
	<div id="layout1" class="mini-layout"
		style="width: 100%; height: 100%;">
		<div title="south" region="south" showHeader="false" height="60px">
			<div class="mini-toolbar"
				style="text-align: center; padding: 6px; border: 0;">
				<div class="mini-toolbar"
					style="text-align: center; padding: 6px; border: 0;">
					<c:if test="${pageState ne 'view'}">
						<a class="mini-button mini-button-iconTop" iconCls="icon-save"
							onClick="save('0')" style="margin-right: 20px;">&nbsp;&nbsp;淇濆瓨&nbsp;&nbsp;</a>
						<a class="mini-button mini-button-iconTop" iconCls="icon-ok"
							onClick="save('1')" style="margin-right: 20px;">&nbsp;&nbsp;鎻愪氦&nbsp;&nbsp;</a>
					</c:if>
					<c:if test="${(parama eq '1' || parama eq '3' || parama eq '4')&& taskid ne null}">
						<a class="mini-button mini-button-iconTop" iconCls="icon-redo"
							style="margin-right: 20px;" onClick="toSubmit('1')">&nbsp;&nbsp;閫氳繃&nbsp;&nbsp;</a>
						<a class="mini-button mini-button-iconTop" iconCls="icon-undo"
							style="margin-right: 20px;" onClick="toSubmit('0')">&nbsp;&nbsp;椹冲洖&nbsp;&nbsp;</a>
					</c:if>
					<c:if test="${(parama eq '2')&& taskid ne null}">
						<a class="mini-button mini-button-iconTop" iconCls="icon-ok"
							style="margin-right: 20px;" onclick="toSubmit('1')">&nbsp;&nbsp;宸茬‘璁?nbsp;&nbsp;</a>
							<a class="mini-button mini-button-iconTop" iconCls="icon-undo"
							style="margin-right: 20px;" onClick="toSubmit('0')">&nbsp;&nbsp;椹冲洖&nbsp;&nbsp;</a>
					</c:if>
					<a class="mini-button mini-button-iconTop" iconCls="icon-close"
						onClick="CloseWindow('cancel')">&nbsp;&nbsp;鍏抽棴&nbsp;&nbsp;</a>
				</div>
			</div>
		</div>
		<c:if test="${piId ne null }">
			<div title="宸ヤ綔娴佺▼,鏌ョ湅璇峰崟鍑绘澶? region="north" height="300"
				showCollapseButton="false" showHeader="false" iconCls="icon-goto"
				expanded="false">
				<%@include
					file="/WEB-INF/pages/cn/com/sinosoft/common/workflowHistory.jsp"%>
			</div>
		</c:if>
<div title="center" region="center">
		<div class="mini-fit">
	<iframe name="post_frame" id="post_frame" style="display: none;"></iframe>
	<form id="form1" name="form1" method="post" target="post_frame"
		enctype="multipart/form-data">
		<s:token></s:token>
		<div style="display: none;">
			<bspHtml:Hidden property="saveType"></bspHtml:Hidden>
			<bspHtml:Hidden property="piId"></bspHtml:Hidden>
			<bspHtml:Hidden property="taskid"></bspHtml:Hidden>
			<bspHtml:Hidden property="auditResult"></bspHtml:Hidden>
			<bspHtml:Hidden property="auditRemark"></bspHtml:Hidden>
			<bspHtml:Hidden property="parama"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.id"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.dataSource"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.dataModyTime"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.piId"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.proState"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.state"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.addZone"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.addOrg"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.addDep"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.addUser"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.addTime"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.modyZone"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.modyOrg"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.modyDep"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.modyUser"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.modyTime"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.shrUser"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.shrRemart"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.shrDate"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.rsbaUser"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.rsbaRemart"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.rsbaDate"></bspHtml:Hidden>
		</div>
	<center>
	<table class="tab-1" cellpadding="5" cellspacing="0" border="1" align="center">
		<COLGROUP>
			<col align="right" style="width:25%"/>
			<col align="left" style="width:25%"/>
			<col align="right" style="width:25%"/>
			<col align="left" style="width:25%"/>
		</COLGROUP>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;鐢宠绉戝锛?			</td>
			<td>
				<bspHtml:ComboBox property="result.applyDep"
					style="width:100%;" enabled="false" required="true"
					requiredErrorText="[鐢ㄥ嵃鐢宠澶勫]涓嶅彲涓虹┖"  
					value="${pageState eq 'add'?SESSION_USERVIEW.departmentId:result.applyDep}"
					valueField="ID" textField="NAME" url="BASE_TAG_DEPARTMENT">
				</bspHtml:ComboBox>
			</td>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;鐢宠浜猴細
			</td>
			<td>
				<bspHtml:TextBox property="result.applyUser"
					style="width:100%;" 
					emptyText="璇疯緭鍏ョ敵璇蜂汉" maxLength="50"
					vtype="rangeChar:0,50;" 
					value="${pageState eq 'add'? SESSION_USERVIEW.name : result.applyUser}"
					required="true" requiredErrorText="[鐢宠浜篯 涓嶈兘涓虹┖"
					rangeCharErrorText="[鐢宠浜篯 瀛楃鏁板繀椤诲湪 {0} 鍒?{1} 涔嬮棿">
				</bspHtml:TextBox>
			</td>
			
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;鎷呬换鑱屽姟锛?			</td>
			<td>
				<bspHtml:TextBox property="result.holdPost"
					style="width:100%;" 
					emptyText="璇疯緭鍏ユ媴浠昏亴鍔? maxLength="50"
					vtype="rangeChar:0,50;"
					required="true" requiredErrorText="[鎷呬换鑱屽姟] 涓嶈兘涓虹┖"
					rangeCharErrorText="[鎷呬换鑱屽姟] 瀛楃鏁板繀椤诲湪 {0} 鍒?{1} 涔嬮棿">
				</bspHtml:TextBox>
			</td>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;鏃堕棿锛?			</td>
			<td>
				<bspHtml:DatePicker property="result.applyTime"
					style="width:100%;" 
					format="yyyy-MM-dd" value="<%=Util.getToDay()%>" emptyText="璇烽€夋嫨鐢宠鏃堕棿">
				</bspHtml:DatePicker>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;鍏艰亴鍗曚綅鍚嶇О鍙婃€ц川锛?			</td>
			<td>
				<bspHtml:TextBox property="result.partJobUnit"
					style="width:100%;"
					emptyText="璇疯緭鍏ュ吋鑱屽崟浣嶅悕绉板強鎬ц川" maxLength="500"
					required="true" requiredErrorText="[鍏艰亴鍗曚綅鍚嶇О鍙婃€ц川] 涓嶈兘涓虹┖"
					vtype="rangeChar:0,500;"
					rangeCharErrorText="[鍏艰亴鍗曚綅鍚嶇О鍙婃€ц川] 瀛楃鏁板繀椤诲湪 {0} 鍒?{1} 涔嬮棿">
				</bspHtml:TextBox>
			</td>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;闅跺睘鍏崇郴鍙婄骇鍒細
			</td>
			<td>
				<bspHtml:TextBox property="result.membership"
					style="width:100%;"
					emptyText="璇疯緭鍏ラ毝灞炲叧绯诲強绾у埆" maxLength="500"
					required="true" requiredErrorText="[闅跺睘鍏崇郴鍙婄骇鍒玗 涓嶈兘涓虹┖"
					vtype="rangeChar:0,500;"
					rangeCharErrorText="[闅跺睘鍏崇郴鍙婄骇鍒玗 瀛楃鏁板繀椤诲湪 {0} 鍒?{1} 涔嬮棿">
				</bspHtml:TextBox>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;鐜版湁鍏艰亴鏁伴噺锛?			</td>
			<td>
				<bspHtml:TextBox property="result.partJobNumber"
					style="width:100%;" 
					emptyText="璇疯緭鍏ョ幇鏈夊吋鑱屾暟閲? maxLength="50"
					vtype="rangeChar:0,50;" 
					required="true" requiredErrorText="[鐜版湁鍏艰亴鏁伴噺] 涓嶈兘涓虹┖"
					rangeCharErrorText="[鐜版湁鍏艰亴鏁伴噺] 瀛楃鏁板繀椤诲湪 {0} 鍒?{1} 涔嬮棿">
				</bspHtml:TextBox>
			</td>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;鏄惁涓虹画鑱橈細
			</td>
			<td>
				<bspHtml:RadioButtonList property="result.reEmploy"
					style="width:100%;"  
					url="RS_RE_EMPLOY" value="1" 
					required="true" requiredErrorText="[鏄惁涓虹画鑱榏 涓嶈兘涓虹┖"
					textField="NAME" valueField="ID">
				</bspHtml:RadioButtonList>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;鐜版湁鍏艰亴鎯呭喌锛?			</td>
			<td colspan="3">
				<bspHtml:TextArea property="result.partJobExist"
					style="width:100%;height:100px;"
					emptyText="濉啓绀轰緥锛氫腑鍗庨闃插尰瀛︿細鏌愭煇鍒嗕細-濮斿憳-鍏艰亴鏃堕棿" maxLength="2000"
					vtype="rangeChar:0,2000;"
					required="true" requiredErrorText="[鐜版湁鍏艰亴鎯呭喌] 涓嶈兘涓虹┖"
					rangeCharErrorText="[鐜版湁鍏艰亴鎯呭喌] 瀛楃鏁板繀椤诲湪 {0} 鍒?{1} 涔嬮棿">
				</bspHtml:TextArea>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;鐢宠鐞嗙敱锛?			</td>
			<td colspan="3">
				<bspHtml:TextArea property="result.applyAccount"
					style="width:100%;height:100px;"
					emptyText="璇疯緭鍏ョ敵璇风悊鐢? maxLength="2000"
					vtype="rangeChar:0,2000;"
					required="true" requiredErrorText="[鐢宠鐞嗙敱] 涓嶈兘涓虹┖"
					rangeCharErrorText="[鐢宠鐞嗙敱] 瀛楃鏁板繀椤诲湪 {0} 鍒?{1} 涔嬮棿">
				</bspHtml:TextArea>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;鍗曚綅鍦板潃/宸ヤ綔鍦扮偣锛?			</td>
			<td colspan="3">
				<bspHtml:TextArea property="result.unitAddress"
					style="width:100%;" 
					emptyText="璇疯緭鍏ュ崟浣嶅湴鍧€/宸ヤ綔鍦扮偣" maxLength="200"
					required="true" requiredErrorText="[鍗曚綅鍦板潃/宸ヤ綔鍦扮偣] 涓嶈兘涓虹┖"
					vtype="rangeChar:0,200;"
					rangeCharErrorText="[鍗曚綅鍦板潃/宸ヤ綔鍦扮偣] 瀛楃鏁板繀椤诲湪 {0} 鍒?{1} 涔嬮棿">
				</bspHtml:TextArea>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;鏄惁棰嗗彇钖叕锛?			</td>
			<td>
				<bspHtml:RadioButtonList property="result.isReceiveSalary"
					style="width:100%;"  
					url="ICMIS_ISORNO" value="1"
					required="true" requiredErrorText="[鏄惁棰嗗彇钖叕] 涓嶈兘涓虹┖"
					textField="NAME" valueField="ID">
				</bspHtml:RadioButtonList>
			</td>
			<c:if test="${sessionScope.SESSION_USERVIEW.position ne '10'}">
				<td align="right" class="bgcolor">
					<font color="red">*</font>&nbsp;鍙戝線鎵€棰嗗锛?				</td>
				<td>
					<bspHtml:ComboBox property="result.leader"
						style="width:100%;"  
						url="CM_ZGSLDSP_USERNAME"
						allowInput="false"
						viewState="edit"
						emptyText="浜轰簨澶勯€夋嫨鍒嗙鎵€棰嗗,鍙互鐩存帴鎻愪氦" 
						textField="NAME" valueField="ID"
						requiredErrorText="[鎵€棰嗗] 涓嶈兘涓虹┖">
					</bspHtml:ComboBox>
				</td>
			</c:if>
		</tr>
	</table>
	</center>
	</form>
	</div>
</div>
</div>
		<!-- 瀹℃壒 -->
	<div id="win1" class="mini-window" title="瀹℃牳"
		style="width: 600px; height: 300px; display: block; position: absolute; top: -999em;"
		showMaxButton="true" showCollapseButton="true" showShadow="true"
		showToolbar="false" showFooter="true" showModal="true"
		allowResize="true" allowDrag="true">
		<table class="tab-1" cellpadding="5" cellspacing="0" border="1"
			align="center">
			<colgroup>
				<col width="25%" />
				<col width="75%" />
			</colgroup>
			<tr>
				<td align="right" class="bgcolor"><font color="red">*</font>&nbsp;瀹℃牳鎰忚锛?/td>
				<td><bspHtml:TextArea property="shyj" viewState="edit"
						emptyText="璇峰～鍐欏鏍告剰瑙? vtype="maxLength:200" required="true"
						requiredErrorText="璇峰～鍐欏鏍告剰瑙? maxLength="200"
						style="width:100%;height:80px;" /></td>
			</tr> 
		</table>
		<div property="footer"
			style="text-align: center; height: 30px; padding-top: 5px;">
			<a class="mini-button" plain="false" iconCls="icon-ok"
				onclick="saveAudit">鎻愪氦</a> &nbsp;&nbsp;&nbsp;&nbsp; <a
				class="mini-button" plain="false" iconCls="icon-close"
				onclick="hideWindow">鍏抽棴</a>
		</div>
	</div>
</body>
</html>