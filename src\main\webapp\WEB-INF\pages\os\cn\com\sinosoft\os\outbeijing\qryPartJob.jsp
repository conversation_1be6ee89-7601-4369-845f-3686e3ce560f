锘?%@page import="cn.com.sinosoft.constant.RsConstant"%>
<%@ page language="java" contentType="text/html; charset=GBK" pageEncoding="GBK"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<jsp:directive.page import="ie.weaf.toolkit.Util" />
	<%@ include file="/common/taglibs.jsp"%>
<%
	String title = "鍏艰亴鐢宠"; // 鏍囬
	String viewFunc = "RS000020201"; // 鏌ョ湅鏉冮檺
	String addFunc = "RS000020202"; // 娣诲姞鏉冮檺
	String edtFunc = "RS000020203"; // 淇敼鏉冮檺
	String delFunc = "RS000020204"; // 鍒犻櫎鏉冮檺
	String submitFunc = "RS000020205"; // 鎻愪氦鏉冮檺
	String revokeFunc = "RS000020206"; //鎾ら攢鐢宠鏉冮檺
	String expFunc = "RS000020207"; // 瀵煎嚭鏉冮檺
	String kyFucn = "RS0000801";//绉戝憳鏌ヨ鏉冮檺
	String kzFucn = "RS0000802";//绉戦暱鏌ヨ鏉冮檺 
	String allFucn = "RS0000803";//瓒呯骇鏌ヨ鏉冮檺
	String isAllFunc = "";
	String isKzFucn = "";
	String isKyFucn = "";
	String opers =  taglibs_userview.getOpers();
	if(!Util.isNullOrEmpty(opers)){
		String arrsOpers[] = opers.split(",");
		for(int i=0;i<arrsOpers.length;i++){
			if(allFucn.equals(arrsOpers[i]) ){
				isAllFunc = "1";
			}
			if(kzFucn.equals(arrsOpers[i])){
				isKzFucn="1";
			}
			if(kyFucn.equals(arrsOpers[i])){
				isKyFucn="1";
			}
		}
	} 
%>
<html>
<head>
	<title><%=title%></title>
	<meta http-equiv="content-type" content="text/html; charset=GBK" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<c:set value="<%=isAllFunc %>" var="isAllFunc"></c:set>
	<c:set value="<%=isKzFucn %>" var="isKzFucn"></c:set>
	<c:set value="<%=isKyFucn %>" var="isKyFucn"></c:set>
	<style type="text/css">
	.myrow {
		 color: gray;
	}
	</style>
	<script type="text/javascript">
		var grid,form,perm = false;
		// 鍒濆鍖栧嚱鏁?		$(document).ready(function() {
			mini.parse();
			
			var n = mini.get("layout1").getRegion("north");
			if(n){
				n.height=$("#qryTable").height()+55;
				mini.layout();
				form = new mini.Form("form1");
			}
			grid = mini.get("datagrid1");
			if (perm) {
				// 鍙屽嚮Grid琛屾墽琛屾煡鐪?
				grid.on("rowdblclick", function(e) {
					view();
				});
			}
			grid.on("drawcell", function (e) {
				var record = e.record;
				//璁剧疆琛屾牱寮?				if (record.STATE_CODE == '0') {
					e.rowCls = "myrow";
				}
			});
			search();
		});


		// 鏌ヨ鍑芥暟
		function search() {
			if(form){
				form.validate();
				if (form.isValid() == false){
					showFormErrorTexts(form.getErrorTexts(),400,300);
					return;
				}
			}
			var ky='',kz='';
			 if('1'=='${isKzFucn}'){
				kz='${SESSION_USERVIEW.departmentId}'
			}else if('1'=='${isKyFucn}'){
				ky='${SESSION_USERVIEW.username}'
			}
			
			grid.load({
				session_KY :encodeURIComponent(ky),
				session_KZ :encodeURIComponent(kz), 
				session_PROSTATE :encodeURIComponent(mini.get("session_PROSTATE").getValue()), //瀹℃牳鐘舵€?				session_PRO_USER :encodeURIComponent('${SESSION_USERVIEW.username}'),
				session_APPLY_USER : encodeURIComponent(mini.get("session_APPLY_USER").getValue()), // 鐢宠浜?				session_APPLY_DEP : encodeURIComponent(mini.get("session_APPLY_DEP").getValue()), // 鐢宠绉戝
				session_APPLY_TIME_BEGIN : encodeURIComponent(mini.get("session_APPLY_TIME_BEGIN").getFormValue()), // 鐢宠鏃堕棿
				session_APPLY_TIME_END : encodeURIComponent(mini.get("session_APPLY_TIME_END").getFormValue()) // 鑷?			},function(){
				//鍔犺浇鎴愬姛涔嬪悗瑕佸仛鐨勪簨鎯?			});
		}

		// 鏌ョ湅
		function view() {
			var records = grid.getSelecteds();
			if (records.length > 1) {
				mini.alert('涓嶅彲鏌ョ湅澶氭潯璁板綍锛?);
				return;
			}
			var record = records[0];
			if (record) {
				var win = mini.open({
					url : "${ctx}/cn/com/sinosoft/rs/partjob/partJob_viewParent.ac?pageState=view&id="
						+ record.ID,
					title : "鏌ョ湅",
					width : 800,
					height : 400,
					showMaxButton : true,
					ondestroy : function(action) {
						// 绐楀彛鍏抽棴鏃舵墽琛?					}
				});
			 win.max();
			} else {
				mini.alert("璇烽€変腑涓€鏉¤褰?);
			}
		}

		// 娣诲姞
		function add() {
			var win = mini.open({
				url : "${ctx}/cn/com/sinosoft/rs/partjob/partJob_addParentInput.ac?pageState=add",
				title : "娣诲姞",
				width : 800,
				height : 400,
				showMaxButton : true,
				ondestroy : function(action) {
					// 绐楀彛鍏抽棴鏃舵墽琛?					if (action == "save"){
						grid.reload();
					}
				}
			});
			 win.max();
		}

		// 淇敼
		function edit() {
			var records = grid.getSelecteds();
			if (records.length > 1) {
				mini.alert('涓嶅彲淇敼澶氭潯璁板綍锛?);
				return;
			}
			var record = records[0];
			if (record) {
				// 淇敼鏉冮檺鎺у埗
				if('<%=RsConstant.PRO_STATE_SAVE%>' !=record.PRO_STATE){
					 mini.alert("鍙兘瀹℃牳鐘舵€佷负銆愬凡淇濆瓨銆戠殑鏁版嵁锛?);
					 return;
				 }
				if("${SESSION_USERVIEW.username}" != record.ADD_USER){
					mini.alert("鍙兘淇敼褰撳墠鐢ㄦ埛淇濆瓨鐨勬暟鎹?);
					 return;
				}
				//鎵撳紑淇敼绐楀彛
				var win = mini.open({
					url : "${ctx}/cn/com/sinosoft/rs/partjob/partJob_edtParentInput.ac?pageState=edit&id="
						+ record.ID,
					title : "淇敼",
					width : 800,
					height : 400,
					showMaxButton : true,
					ondestroy : function(action) {
						// 绐楀彛鍏抽棴鏃舵墽琛?						if (action == "save"){
							grid.reload();
						}
					}
				});
				 win.max();
			} else {
				mini.alert("璇烽€変腑涓€鏉¤褰?);
			}
		}

		// 鍒犻櫎
		function remove() {
			var rows = grid.getSelecteds();
			if (rows.length > 0) {
				// 鍒犻櫎鏉冮檺鎺у埗
				// 鏃犲垹闄ゆ潈闄愭帶鍒?				// 鍒犻櫎纭
				// 瀹℃牳涓姸鎬?<%=RsConstant.PRO_STATE_SAVE%>' !=rows[i].PRO_STATE 
				mini.confirm ("纭畾鍒犻櫎閫変腑鐨? + rows.length + "鏉¤褰曪紵","鍒犻櫎璁板綍",
					function(action) {
						if (action == "ok") {
							var id = [];
							for (var i = 0, l = rows.length; i < l; i++) {
								if("${SESSION_USERVIEW.username}" != rows[i].ADD_USER){
									mini.alert("鍙兘鍒犻櫎褰撳墠鐢ㄦ埛淇濆瓨鐨勬暟鎹?);
									 return;
								}
								if (rows[i].PRO_STATE_NAME != "宸蹭繚瀛? && rows[i].PRO_STATE_NAME != "鐢宠浜鸿姝?) {
									mini.alert("鍙兘鍒犻櫎銆愮敵璇蜂汉璁㈡銆戠殑鏁版嵁锛?);
									 return;
								}
								id.push(rows[i].ID);
							}
							id = id.join(',');
							//鎵ц鍒犻櫎
							grid.loading("鍒犻櫎涓紝璇风◢鍚?.....");
							$.ajax({
								url : "${ctx}/cn/com/sinosoft/rs/partjob/partJob_delParentSubmit.ac?id="
									+ id,
								success : function() {
									grid.reload();
								},
								error : function(XMLHttpRequest, textStatus, errorThrown) {

								}
							});
						}
					}
				);
			} else {
				mini.alert("璇烽€夋嫨瑕佸垹闄ょ殑璁板綍");
			}
		}
		//鎻愪氦
		function submitApply(){
			var rows = grid.getSelecteds();
			if (rows.length > 0) {
				 
				mini.confirm ("纭畾鎻愪氦閫変腑鐨? + rows.length + "鏉¤褰曪紵","鎻愪氦璁板綍",
					function(action) {
						if (action == "ok") {
							var id = [];
							for (var i = 0, l = rows.length; i < l; i++) {
								if('<%=RsConstant.PRO_STATE_SAVE%>' !=rows[i].PRO_STATE){
									 mini.alert("鍙兘鎻愪氦鐘舵€佷负銆愬凡淇濆瓨銆戠殑鏁版嵁锛?);
									 return;
								 }
								if("${SESSION_USERVIEW.username}" != rows[i].ADD_USER){
									mini.alert("鍙兘鎻愪氦褰撳墠鐢ㄦ埛淇濆瓨鐨勬暟鎹?);
									 return;
								}
								id.push(rows[i].ID);
							}
							id = id.join(',');
							//鎵ц鍒犻櫎
							grid.loading("鎻愪氦涓紝璇风◢鍚?.....");
							$.ajax({
								url : "${ctx}/cn/com/sinosoft/rs/partjob/partJob_submitApply.ac?id="
									+ id,
								success : function() {
									grid.reload();
								},
								error : function(XMLHttpRequest, textStatus, errorThrown) {

								}
							});
						}
					}
				);
			} else {
				mini.alert("璇烽€夋嫨瑕佸垹闄ょ殑璁板綍");
			}
		}
		//鎾ら攢鐢宠
		function revokeApply(){
			var rows = grid.getSelecteds();
			if (rows.length > 0) {
				 
				mini.confirm ("纭畾鎾ら攢閫変腑鐨? + rows.length + "鏉＄敵璇疯褰曪紵","鎾ら攢鐢宠璁板綍",
					function(action) {
						if (action == "ok") {
							var id = [];
							for (var i = 0, l = rows.length; i < l; i++) {
								if('<%=RsConstant.PRO_STATE_SUBMIT%>' !=rows[i].PRO_STATE){
									 mini.alert("鍙兘鎾ら攢鐘舵€佷负銆愬凡鎻愪氦銆戠殑鐢宠璁板綍锛?);
									 return;
								 }
								if("${SESSION_USERVIEW.username}" != rows[i].ADD_USER){
									mini.alert("鍙兘鎾ら攢褰撳墠鐢ㄦ埛淇濆瓨鐨勬暟鎹?);
									 return;
								}
								id.push(rows[i].ID);
							}
							id = id.join(',');
							//鎵ц鍒犻櫎
							grid.loading("鎾ら攢涓紝璇风◢鍚?.....");
							$.ajax({
								url : "${ctx}/cn/com/sinosoft/rs/partjob/partJob_revokeApply.ac?id="
									+ id,
								success : function() {
									grid.reload();
								},
								error : function(XMLHttpRequest, textStatus, errorThrown) {

								}
							});
						}
					}
				);
			} else {
				mini.alert("璇烽€夋嫨瑕佸垹闄ょ殑璁板綍");
			}
		}
	</script>
</head>
<body>
	<!-- 瀵煎嚭Excel鐩稿叧HTML start -->
	<form id="excelForm" enctype="multipart/form-data" 
		action="${ctx }/util/util_exportExcle.ac" method="post" 
		target="excelIFrame" style="display:none;">
		<!-- 淇濆瓨鍒椾俊鎭?-->
		<input type="hidden" name="headData" id="headData" />
		<!-- 淇濆瓨鍒楄〃鏁版嵁锛堜繚鐣欓」锛岀洰鍓嶅凡搴熷純锛?-->
		<input type="hidden" name="bodyData" id="bodyData" />
		<!-- 涓嶅鍑虹殑鏄剧ず鍒楀瓧娈礷ield锛岄€楀彿鍒嗛殧 -->
		<input type="hidden" name="export_hideColumn" id="export_hideColumn"  />
		<!-- 瑕佸鍑虹殑闅愯棌鍒楀瓧娈礷ield锛岄€楀彿鍒嗛殧 -->
		<input type="hidden" name="export_showColumn" id="export_showColumn" />
	</form>
	<iframe id="excelIFrame" name="excelIFrame" style="display:none;"></iframe>
	<!-- 瀵煎嚭Excel鐩稿叧HTML end -->
	<div id="layout1" class="mini-layout" style="width: 100%; height: 100%;">
		<div title="<%=title%>" region="north" height="72" showHeader="true" class="util_search" >
			<div id="form1" style="padding: 0;margin: 0;">
				<table id="qryTable" align="center" style="margin-top: 6px;" cellpadding="2">
					<tr>
						<td align="right"><nobr>&nbsp;鐢宠绉戝锛?/nobr></td>
						<td>
							<bspHtml:ComboBox property="session_APPLY_DEP"  style="width:100%;"
								 valueField="ID" textField="NAME" url="BASE_TAG_DEPARTMENT"
								 value="${isAllFunc=='1'?'':SESSION_USERVIEW.departmentId}" 
								 enabled="${isAllFunc=='1'?true:false}"
								 >
							</bspHtml:ComboBox>
						</td>
						<td align="right"><nobr>&nbsp;鐢宠浜猴細</nobr></td>
						<td>
							<bspHtml:TextBox property="session_APPLY_USER"
								onenter="search()" vtype="maxLength:38;rangeChar:0,4000;" maxLength="4000"
								maxLengthErrorText="[鐢宠浜篯 涓嶈兘瓒呰繃 {0} 涓瓧绗?
								rangeCharErrorText="[鐢宠浜篯 瀛楃鏁板繀椤诲湪 {0} 鍒?{1} 涔嬮棿" style="width:100%">
							</bspHtml:TextBox>
						</td>
						
						<td align="right"><nobr>&nbsp;鐢宠鏃堕棿锛?/nobr></td>
						<td>
							<bspHtml:DatePicker property="session_APPLY_TIME_BEGIN"
								format="yyyy-MM-dd"
								value="<%=Util.getMonthFirstDay()%>" style="width:120px">
							</bspHtml:DatePicker>
						</td>
						<td align="center"><nobr>&nbsp;鑷?/nobr></td>
						<td colspan="1">
							<bspHtml:DatePicker property="session_APPLY_TIME_END"
								format="yyyy-MM-dd"
								value="<%=Util.getToDay()%>" style="width:120px">
							</bspHtml:DatePicker>
						</td>
						<td align="right"><nobr>&nbsp;瀹℃牳鐘舵€侊細</nobr></td>
						<td>
							<bspHtml:ComboBox property="session_PROSTATE" valueField="ID" textField="NAME" style="width:100%"
							showNullItem="true" nullItemText="鍏ㄩ儴"  value="0,1,2,3" url="RS_PRO_STATE" multiSelect="true"></bspHtml:ComboBox>
						</td>
						<td align="center" >
							<a class="mini-button" iconCls="icon-search" onclick="search()">鏌ヨ</a>
						</td>
						
					</tr>
				</table>
			</div>
		</div>
		<div title="<%=title%>" region="center" showHeader="false">
			<div class="mini-toolbar" style="padding: 2px;" borderStyle="border-left:0;border-top:0;border-right:0;">
				<perm:permission funcID="<%=viewFunc %>">
					<script>perm = true;</script>
					<a class="mini-button" iconCls="icon-node" plain="true" onclick="view()">鏌ョ湅</a>
				</perm:permission>
				<perm:permission funcID="<%=addFunc %>">
					<a class="mini-button" iconCls="icon-add" plain="true" onclick="add()">娣诲姞</a>
				</perm:permission>
				<perm:permission funcID="<%=edtFunc %>">
					<a class="mini-button" iconCls="icon-edit" plain="true" onclick="edit()">淇敼</a>
				</perm:permission>
				<perm:permission funcID="<%=delFunc %>">
					<a class="mini-button" iconCls="icon-remove" plain="true" onclick="remove()">鍒犻櫎</a>
				</perm:permission>
				<perm:permission funcID="<%=expFunc %>">
					<a class="mini-button" iconCls="icon-download" plain="true" onclick="exportExcel(grid)">瀵煎嚭Excel</a>
				</perm:permission>
				<perm:permission funcID="<%=submitFunc %>">
					<a class="mini-button" iconCls="icon-ok" plain="true" onclick="submitApply()">鎻愪氦</a>
				</perm:permission>
				<perm:permission funcID="<%=revokeFunc %>">
					<a class="mini-button" iconCls="icon-no" plain="true" onclick="revokeApply()">鎾ら攢鐢宠</a>
				</perm:permission>
				<perm:permission funcID="<%=expFunc %>">
					<a class="mini-button" iconCls="icon-download" plain="true" onclick="exportExcel(grid)">瀵煎嚭Excel</a>
				</perm:permission>
			</div>
			<!--鎾戞弧椤甸潰-->
			<div class="mini-fit">
				<div id="datagrid1" class="mini-datagrid" idField="ID" sortMode="client"
					 allowAlternating="true" url="${ctx }/cn/com/sinosoft/rs/partjob/qryPartJobList.ac"
					 style="width: 100%; height: 100%;" sizeList="[20,50,100]" pageSize="20"
					 multiSelect="true" borderStyle="border:0" selectOnLoad="true">
					<div property="columns">
						<div type="checkcolumn" align="center" headerAlign="center" width="50"></div>
						<div type="indexcolumn" align="center" headerAlign="center" width="50">搴忓彿</div>
						<div header="鐢宠浜? field="APPLY_USER" headerAlign="center">
						</div>
						<div header="鐢宠绉戝" field="APPLY_DEP_NAME" headerAlign="center">
						</div>
						<div header="鐢宠鏃堕棿" field="APPLY_TIME" headerAlign="center" dateFormat="yyyy-MM-dd"
							 dataType="date" align="center" width="150">
						</div>
						<!--<div header="鍗曚綅鍦板潃/宸ヤ綔鍦扮偣" field="UNIT_ADDRESS" headerAlign="center">
						</div>-->
						<div header="鍏艰亴鍗曚綅鍚嶇О鍙婃€ц川" field="PART_JOB_UNIT" headerAlign="center">
						</div>
						<div header="鎷呬换鑱屽姟" field="HOLD_POST" headerAlign="center">
						</div>
						<div header="娴佺▼鐘舵€? field="PRO_STATE_NAME" headerAlign="center">
						</div>
						<div header="娣诲姞鏃堕棿" field="ADD_TIME" headerAlign="center" dateFormat="yyyy-MM-dd HH:mm:ss"
							 dataType="date" align="center" width="150">
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</body>
</html>