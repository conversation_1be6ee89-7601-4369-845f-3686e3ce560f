package cn.com.sinosoft.os.beijingexitapply.service;

import cn.com.sinosoft.os.beijingexitapply.model.BeijingExitApply;
import java.util.List;

/**
 * �������� - service�ӿ�.
 *
 * <AUTHOR>
 * @date: 2025/08/02 15:41:43
 * @version V1.0
 */
public interface BeijingExitApplyService {

	/**
	 * �������� - ��ȡ��������.
	 *
	 * <AUTHOR>
	 * @param id
	 *			     ����
	 * @return �������� ����
	 */
	BeijingExitApply get(String id);

	/**
	 * �������� - ɾ��.
	 *
	 * <AUTHOR>
	 * @param id
	 *			     ����
	 */
	void delete(String id);

	/**
	 * �������� - ����.
	 *
	 * <AUTHOR>
	 * @param result
	 *			     �������� ����
	 */
	void save(BeijingExitApply result);

	/**
	 * �������� - �޸�.
	 *
	 * <AUTHOR>
	 * @param result
	 *			     �������� ����
	 */
	void edit(BeijingExitApply result);

}