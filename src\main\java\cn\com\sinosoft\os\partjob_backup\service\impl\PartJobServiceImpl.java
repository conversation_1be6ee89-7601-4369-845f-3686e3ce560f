package cn.com.sinosoft.rs.partjob.service.impl;

import ie.bsp.core.bean.UserView;
import ie.bsp.frame.dao.CommonBaseDao;
import ie.bsp.ui.FrameConstant;
import ie.weaf.toolkit.Util;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.struts2.ServletActionContext;

import cn.com.sinosoft.constant.IcmisModels;
import cn.com.sinosoft.constant.RsConstant;
import cn.com.sinosoft.mywork.model.WorkFlowConstant;
import cn.com.sinosoft.mywork.service.WorkflowService;
import cn.com.sinosoft.rs.partjob.model.PartJob;
import cn.com.sinosoft.rs.partjob.service.PartJobService;

/**
 * 兼职管理 - service接口实现.
 *
 * <AUTHOR>
 * @date: 2023/12/21 11:09:09
 * @version V1.0
 */
public class PartJobServiceImpl implements PartJobService {

	// 通用dao.
	private CommonBaseDao dao;

	// 工作流service.
	private WorkflowService wfservice;

	/**
	 * 获取wfservice
	 * 
	 * @return wfservice wfservice
	 */
	public WorkflowService getWfservice() {
		return wfservice;
	}

	/**
	 * 设置wfservice
	 * 
	 * @param wfservice wfservice
	 */
	public void setWfservice(WorkflowService wfservice) {
		this.wfservice = wfservice;
	}
	
	/**
	 * 获取 通用dao.
	 *
	 * @return 通用dao
	 */
	public CommonBaseDao getDao() {
		return dao;
	}

	/**
	 * 设置 通用dao.
	 *
	 * @param dao
	 *			     通用dao
	 */
	public void setDao(CommonBaseDao dao) {
		this.dao = dao;
	}

	@Override
	public PartJob get(String id) {
		return (PartJob) dao.get(PartJob.class,id);
	}

	@Override
	public void delete(String ids) {
		String[] arrId = ids.split(",");
		
		for (int i = 0; i < arrId.length; i++) {
			dao.excuteSql("update RS_PART_JOB set STATE='0',PRO_STATE='"+RsConstant.PRO_STATE_DELETE+"' where 1=1 "
					+ " and ID = '" + arrId[i] + "'");
			PartJob result =  get(arrId[i]);
			if (result.getPiId() != null) {
				wfservice.delProcinst(result.getPiId());
			}
		}
	}

	@Override
	public void save(PartJob result) {
		UserView user = (UserView) ServletActionContext.getRequest().getSession().
				getAttribute(FrameConstant.SESSION_USERVIEW);
		String saveType = ServletActionContext.getRequest().getParameter("saveType");
		result.setAddZone(user.getZonecode());
		result.setAddOrg(user.getOrgcode());
		result.setAddDep(user.getDepartmentId());
		result.setAddUser(user.getUsername());
		result.setAddTime(new Date());
		result.setState("1");
		result.setId(ie.bsp.util.UUID.randomUUID().toString());
		result.setProState(RsConstant.PRO_STATE_SAVE);//已保存
		Map<String, Object> map = new HashMap<String, Object>();
		if("1".equals(saveType)){
			startProcess(result,map);
		}
		dao.save(result);
	}

	@Override
	public void edit(PartJob result) {
		String saveType = ServletActionContext.getRequest().getParameter("saveType");
		UserView user = (UserView) ServletActionContext.getRequest().getSession()
				.getAttribute(FrameConstant.SESSION_USERVIEW);
		result.setModyZone(user.getZonecode());
		result.setModyOrg(user.getOrgcode());
		result.setModyDep(user.getDepartmentId());
		result.setModyUser(user.getUsername());
		result.setModyTime(new Date());
		Map<String, Object> map = new HashMap<String, Object>();
		if("1".equals(saveType)){
			if(Util.isNullOrEmpty(result.getPiId())){
				startProcess(result,map);
			}else{
				String taskid = ServletActionContext.getRequest().getParameter("taskid");
				if("10".equals(user.getPosition())){
					map.put("DEP_CODE", result.getApplyDep()); //打印部门
				}else{
					map.put("DEP_CODE", ""); 
					//map.put("user3", result.getShrUser());//选择部门领导
				}
				
				//流程处理
				wfservice.handleTaskWithMap(taskid, "1", "通过", null, map);
			}
		}
		dao.edit(result);
	}
	
	@Override
	public PartJob getPiId(String piId) {
		String hql = "from PartJob b where b.piId = '" + piId + "'";
		List<PartJob> list = dao.qryByHql(hql);
		if (list != null) {
			return list.iterator().next();
		}
		return null;
	}

	@Override
	public void applySubmit(PartJob result, String parama, String taskid,String piId) {
		Map<String, Object> map = new HashMap<String, Object>();
		String auditRemark = ServletActionContext.getRequest().getParameter("auditRemark");
		String auditResult = ServletActionContext.getRequest().getParameter("auditResult");
		UserView user = (UserView) ServletActionContext.getRequest().getSession()
				.getAttribute(FrameConstant.SESSION_USERVIEW);
		if("0".equals(parama)){//首次提交
			auditResult="1";
			auditRemark="已同意";
			map.put("DEP_CODE", result.getApplyDep()); //打印部门
		}else{
			//审核通过
			if ("1".equals(auditResult)) {
				if("1".equals(parama)||"3".equals(parama) || "4".equals(parama)){//推到部门领导或分公司处理
					result.setProState(RsConstant.PRO_STATE_AUDIT);//审核中
					map.put("DEP_CODE",""); //去掉部门
					map.put("user3", result.getLeader());//选择分管领导
					result.setShrDate(new Date());
					result.setShrRemart(auditRemark);
					result.setShrUser(user.getUsername());
					 if("3".equals(parama)){
						 result.setProState(RsConstant.PRO_STATE_OVER);
					 }
				}else if("2".equals(parama)){//分公司处理
					result.setProState(RsConstant.PRO_STATE_OVER);//已完成
					result.setRsbaDate(new Date());
					result.setRsbaRemart("已确认");
					result.setRsbaUser(user.getUsername());
				} 
			} else{
				result.setProState(RsConstant.PRO_STATE_AUDIT);//审核中
				//不通过时全部清空
				auditResult="2";
				resetApply(result);
			}
		}
		dao.edit(result);//修改
		//流程处理
		wfservice.handleTaskWithMap(taskid, auditResult, auditRemark, null, map);
	}

	/**
	 * 
	 * @Title: resetApply
	 * @Description: 重置申请
	 * @author: wxm
	 * @return: void
	 * @exception：
	 */
	public void resetApply(PartJob result){
		result.setShrDate(null);
		result.setShrRemart(null);
		result.setShrUser(null);
		result.setRsbaDate(null);
		result.setRsbaRemart(null);
		result.setRsbaUser(null);
	}
	
	public void startProcess(PartJob result,Map<String, Object> map){
		UserView user = (UserView) ServletActionContext.getRequest().getSession()
				.getAttribute(FrameConstant.SESSION_USERVIEW);
		String targetFilePath = ServletActionContext.getRequest().getSession().getServletContext().getRealPath("/")
				+ IcmisModels.TEMP_FOLD + "/"; // 获取项目临时文件目录
		map.put(WorkFlowConstant.START_USER, result.getAddUser());//申请人
		map.put(WorkFlowConstant.TASK_NAME, "兼职申请流程");// 流程名称
		if("10".equals(user.getPosition())){ //职员
			map.put("DEP_CODE", result.getApplyDep()); //打印部门
			map.put("step", "1");
			// map.put("user2", "MJ88132140");//人事处-孟洁
			map.put("user2", "ZXQ2272");//人事处-张雪琦
		}else{//中层领导
			map.put("step", "2");
			// map.put("user3", "KB79481930");//选择部门领导 默认分管
			map.put("user3", result.getLeader());//选择分管领导
		}
		String pid = wfservice.getWorkflowBaseService().startProcess("rsPartjob", map, targetFilePath); // 启动流程
		result.setPiId(pid);
		result.setProState(RsConstant.PRO_STATE_SUBMIT);//已提交
	}

	@Override
	public void revokeApply(String id) {
		if(!Util.isNullOrEmpty(id)){
			String[] ids = id.split(",");
			for(int i=0;i<ids.length;i++){
				//删除流程
				PartJob result =  get(ids[i]);
				//删除流程实例
				if(!Util.isNullOrEmpty(result.getPiId())){
					wfservice.delProcinst(result.getPiId());
				}
				String zgs = result.getShrUser();
				resetApply(result);
				result.setPiId(null);
				result.setProState(RsConstant.PRO_STATE_SAVE);//保存状态
				result.setShrUser(zgs);
				dao.edit(result);
			}
		}
	}

	@Override
	public void submitApply(String id) {
		if(!Util.isNullOrEmpty(id)){
			String[] ids = id.split(",");
			for(int i=0;i<ids.length;i++){
				PartJob result =  get(ids[i]);
				Map<String, Object> map = new HashMap<String, Object>();
				startProcess(result, map);
				dao.edit(result);
			}
		}
	}
}