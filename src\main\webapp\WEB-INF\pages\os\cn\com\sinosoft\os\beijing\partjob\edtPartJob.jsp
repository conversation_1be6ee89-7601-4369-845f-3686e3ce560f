<%@ page language="java" contentType="text/html; charset=GBK" pageEncoding="GBK"%>
<%@ page import="ie.weaf.toolkit.Util"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
	<title>��ְ���� - ά��</title>
	<meta http-equiv="content-type" content="text/html; charset=GBK" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<%@ include file="/common/taglibs.jsp"%>
	<script type="text/javascript">
		var form;
		$(document).ready(function() {
			mini.parse();
			form = new mini.Form("form1");
			$("#znksTd1").show();
		});
		//����
		function save(e) {
			form.validate();
			if (form.isValid() == false) {
				//��ʾ��֤������Ϣ��400Ϊ��ʾ����ȣ�300Ϊ��ʾ��߶�
				showFormErrorTexts(form.getErrorTexts(),400,300);
				form.getErrors()[0].focus();
				return;
			}
			mini.get("saveType").setValue(e);//�ύ��ʽ
			if ("${pageState}" == "add") {
				document.form1.action = "${ctx}/cn/com/sinosoft/os/beijing/partjob/partJob_addParentSubmit.ac";
			} else {
				document.form1.action = "${ctx}/cn/com/sinosoft/os/beijing/partjob/partJob_edtParentSubmit.ac";
			}
			document.form1.submit();
			waitClick();
		}
		
		//����˴���
		function toSubmit(type){
			mini.get("auditResult").setValue(type);
			if("1"==type){
				mini.get("shyj").setValue("ͬ��");
			}else{
				mini.get("shyj").setValue("��ͬ��");
			}
			var win = mini.get("win1");
			//��ʾ���window
			win.showAtPos("center", "middle");
		}
		//�ر�
		  function hideWindow() {
		      var win = mini.get("win1");
		      win.hide();
		  }
		// �����ύ
		function saveAudit(e) {
			mini.get("auditRemark").setValue(mini.get("shyj").getValue());

			document.form1.action = "${ctx}/cn/com/sinosoft/os/beijing/partjob/partJobSubmit.ac";
			document.form1.submit();
			waitClick();
		}
		
	</script>
</head>
<body>
	<div id="layout1" class="mini-layout"
		style="width: 100%; height: 100%;">
		<div title="south" region="south" showHeader="false" height="60px">
			<div class="mini-toolbar"
				style="text-align: center; padding: 6px; border: 0;">
				<div class="mini-toolbar"
					style="text-align: center; padding: 6px; border: 0;">
					<c:if test="${pageState ne 'view'}">
						<a class="mini-button mini-button-iconTop" iconCls="icon-save"
							onClick="save('0')" style="margin-right: 20px;">&nbsp;&nbsp;����&nbsp;&nbsp;</a>
						<a class="mini-button mini-button-iconTop" iconCls="icon-ok"
							onClick="save('1')" style="margin-right: 20px;">&nbsp;&nbsp;�ύ&nbsp;&nbsp;</a>
					</c:if>
					<c:if test="${(parama eq '1' || parama eq '3' || parama eq '4')&& taskid ne null}">
						<a class="mini-button mini-button-iconTop" iconCls="icon-redo"
							style="margin-right: 20px;" onClick="toSubmit('1')">&nbsp;&nbsp;ͨ��&nbsp;&nbsp;</a>
						<a class="mini-button mini-button-iconTop" iconCls="icon-undo"
							style="margin-right: 20px;" onClick="toSubmit('0')">&nbsp;&nbsp;����&nbsp;&nbsp;</a>
					</c:if>
					<c:if test="${(parama eq '2')&& taskid ne null}">
						<a class="mini-button mini-button-iconTop" iconCls="icon-ok"
							style="margin-right: 20px;" onclick="toSubmit('1')">&nbsp;&nbsp;��ȷ��&nbsp;&nbsp;</a>
							<a class="mini-button mini-button-iconTop" iconCls="icon-undo"
							style="margin-right: 20px;" onClick="toSubmit('0')">&nbsp;&nbsp;����&nbsp;&nbsp;</a>
					</c:if>
					<a class="mini-button mini-button-iconTop" iconCls="icon-close"
						onClick="CloseWindow('cancel')">&nbsp;&nbsp;�ر�&nbsp;&nbsp;</a>
				</div>
			</div>
		</div>
		<c:if test="${piId ne null }">
			<div title="��������,�鿴�뵥���˴�" region="north" height="300"
				showCollapseButton="false" showHeader="false" iconCls="icon-goto"
				expanded="false">
				<%@include
					file="/WEB-INF/pages/cn/com/sinosoft/common/workflowHistory.jsp"%>
			</div>
		</c:if>
<div title="center" region="center">
		<div class="mini-fit">
	<iframe name="post_frame" id="post_frame" style="display: none;"></iframe>
	<form id="form1" name="form1" method="post" target="post_frame"
		enctype="multipart/form-data">
		<s:token></s:token>
		<div style="display: none;">
			<bspHtml:Hidden property="saveType"></bspHtml:Hidden>
			<bspHtml:Hidden property="piId"></bspHtml:Hidden>
			<bspHtml:Hidden property="taskid"></bspHtml:Hidden>
			<bspHtml:Hidden property="auditResult"></bspHtml:Hidden>
			<bspHtml:Hidden property="auditRemark"></bspHtml:Hidden>
			<bspHtml:Hidden property="parama"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.id"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.dataSource"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.dataModyTime"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.piId"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.proState"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.state"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.addZone"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.addOrg"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.addDep"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.addUser"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.addTime"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.modyZone"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.modyOrg"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.modyDep"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.modyUser"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.modyTime"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.shrUser"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.shrRemart"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.shrDate"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.rsbaUser"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.rsbaRemart"></bspHtml:Hidden>
			<bspHtml:Hidden property="result.rsbaDate"></bspHtml:Hidden>
		</div>
	<center>
	<table class="tab-1" cellpadding="5" cellspacing="0" border="1" align="center">
		<COLGROUP>
			<col align="right" style="width:25%"/>
			<col align="left" style="width:25%"/>
			<col align="right" style="width:25%"/>
			<col align="left" style="width:25%"/>
		</COLGROUP>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;������ң�
			</td>
			<td>
				<bspHtml:ComboBox property="result.applyDep"
					style="width:100%;" enabled="false" required="true"
					requiredErrorText="[��ӡ���봦��]����Ϊ��"  
					value="${pageState eq 'add'?SESSION_USERVIEW.departmentId:result.applyDep}"
					valueField="ID" textField="NAME" url="BASE_TAG_DEPARTMENT">
				</bspHtml:ComboBox>
			</td>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;�����ˣ�
			</td>
			<td>
				<bspHtml:TextBox property="result.applyUser"
					style="width:100%;" 
					emptyText="������������" maxLength="50"
					vtype="rangeChar:0,50;" 
					value="${pageState eq 'add'? SESSION_USERVIEW.name : result.applyUser}"
					required="true" requiredErrorText="[������] ����Ϊ��"
					rangeCharErrorText="[������] �ַ��������� {0} �� {1} ֮��">
				</bspHtml:TextBox>
			</td>
			
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;����ְ��
			</td>
			<td>
				<bspHtml:TextBox property="result.holdPost"
					style="width:100%;" 
					emptyText="�����뵣��ְ��" maxLength="50"
					vtype="rangeChar:0,50;"
					required="true" requiredErrorText="[����ְ��] ����Ϊ��"
					rangeCharErrorText="[����ְ��] �ַ��������� {0} �� {1} ֮��">
				</bspHtml:TextBox>
			</td>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;ʱ�䣺
			</td>
			<td>
				<bspHtml:DatePicker property="result.applyTime"
					style="width:100%;" 
					format="yyyy-MM-dd" value="<%=Util.getToDay()%>" emptyText="��ѡ������ʱ��">
				</bspHtml:DatePicker>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;��ְ��λ���Ƽ����ʣ�
			</td>
			<td>
				<bspHtml:TextBox property="result.partJobUnit"
					style="width:100%;"
					emptyText="�������ְ��λ���Ƽ�����" maxLength="500"
					required="true" requiredErrorText="[��ְ��λ���Ƽ�����] ����Ϊ��"
					vtype="rangeChar:0,500;"
					rangeCharErrorText="[��ְ��λ���Ƽ�����] �ַ��������� {0} �� {1} ֮��">
				</bspHtml:TextBox>
			</td>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;������ϵ������
			</td>
			<td>
				<bspHtml:TextBox property="result.membership"
					style="width:100%;"
					emptyText="������������ϵ������" maxLength="500"
					required="true" requiredErrorText="[������ϵ������] ����Ϊ��"
					vtype="rangeChar:0,500;"
					rangeCharErrorText="[������ϵ������] �ַ��������� {0} �� {1} ֮��">
				</bspHtml:TextBox>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;���м�ְ������
			</td>
			<td>
				<bspHtml:TextBox property="result.partJobNumber"
					style="width:100%;" 
					emptyText="���������м�ְ����" maxLength="50"
					vtype="rangeChar:0,50;" 
					required="true" requiredErrorText="[���м�ְ����] ����Ϊ��"
					rangeCharErrorText="[���м�ְ����] �ַ��������� {0} �� {1} ֮��">
				</bspHtml:TextBox>
			</td>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;�Ƿ�Ϊ��Ƹ��
			</td>
			<td>
				<bspHtml:RadioButtonList property="result.reEmploy"
					style="width:100%;"  
					url="RS_RE_EMPLOY" value="1" 
					required="true" requiredErrorText="[�Ƿ�Ϊ��Ƹ] ����Ϊ��"
					textField="NAME" valueField="ID">
				</bspHtml:RadioButtonList>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;���м�ְ�����
			</td>
			<td colspan="3">
				<bspHtml:TextArea property="result.partJobExist"
					style="width:100%;height:100px;"
					emptyText="��дʾ�����л�Ԥ��ҽѧ��ĳĳ�ֻ�-ίԱ-��ְʱ��" maxLength="2000"
					vtype="rangeChar:0,2000;"
					required="true" requiredErrorText="[���м�ְ���] ����Ϊ��"
					rangeCharErrorText="[���м�ְ���] �ַ��������� {0} �� {1} ֮��">
				</bspHtml:TextArea>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;�������ɣ�
			</td>
			<td colspan="3">
				<bspHtml:TextArea property="result.applyAccount"
					style="width:100%;height:100px;"
					emptyText="��������������" maxLength="2000"
					vtype="rangeChar:0,2000;"
					required="true" requiredErrorText="[��������] ����Ϊ��"
					rangeCharErrorText="[��������] �ַ��������� {0} �� {1} ֮��">
				</bspHtml:TextArea>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;��λ��ַ/�����ص㣺
			</td>
			<td colspan="3">
				<bspHtml:TextArea property="result.unitAddress"
					style="width:100%;" 
					emptyText="�����뵥λ��ַ/�����ص�" maxLength="200"
					required="true" requiredErrorText="[��λ��ַ/�����ص�] ����Ϊ��"
					vtype="rangeChar:0,200;"
					rangeCharErrorText="[��λ��ַ/�����ص�] �ַ��������� {0} �� {1} ֮��">
				</bspHtml:TextArea>
			</td>
		</tr>
		<tr>
			<td align="right" class="bgcolor">
				<font color="red">*</font>&nbsp;�Ƿ���ȡн�꣺
			</td>
			<td>
				<bspHtml:RadioButtonList property="result.isReceiveSalary"
					style="width:100%;"  
					url="ICMIS_ISORNO" value="1"
					required="true" requiredErrorText="[�Ƿ���ȡн��] ����Ϊ��"
					textField="NAME" valueField="ID">
				</bspHtml:RadioButtonList>
			</td>
			<c:if test="${sessionScope.SESSION_USERVIEW.position ne '10'}">
				<td align="right" class="bgcolor">
					<font color="red">*</font>&nbsp;�������쵼��
				</td>
				<td>
					<bspHtml:ComboBox property="result.leader"
						style="width:100%;"  
						url="CM_ZGSLDSP_USERNAME"
						allowInput="false"
						viewState="edit"
						emptyText="���´�ѡ��ֹ����쵼,����ֱ���ύ" 
						textField="NAME" valueField="ID"
						requiredErrorText="[���쵼] ����Ϊ��">
					</bspHtml:ComboBox>
				</td>
			</c:if>
		</tr>
	</table>
	</center>
	</form>
	</div>
</div>
</div>
		<!-- ���� -->
	<div id="win1" class="mini-window" title="���"
		style="width: 600px; height: 300px; display: block; position: absolute; top: -999em;"
		showMaxButton="true" showCollapseButton="true" showShadow="true"
		showToolbar="false" showFooter="true" showModal="true"
		allowResize="true" allowDrag="true">
		<table class="tab-1" cellpadding="5" cellspacing="0" border="1"
			align="center">
			<colgroup>
				<col width="25%" />
				<col width="75%" />
			</colgroup>
			<tr>
				<td align="right" class="bgcolor"><font color="red">*</font>&nbsp;��������</td>
				<td><bspHtml:TextArea property="shyj" viewState="edit"
						emptyText="����д������" vtype="maxLength:200" required="true"
						requiredErrorText="����д������" maxLength="200"
						style="width:100%;height:80px;" /></td>
			</tr> 
		</table>
		<div property="footer"
			style="text-align: center; height: 30px; padding-top: 5px;">
			<a class="mini-button" plain="false" iconCls="icon-ok"
				onclick="saveAudit">�ύ</a> &nbsp;&nbsp;&nbsp;&nbsp; <a
				class="mini-button" plain="false" iconCls="icon-close"
				onclick="hideWindow">�ر�</a>
		</div>
	</div>
</body>
</html>