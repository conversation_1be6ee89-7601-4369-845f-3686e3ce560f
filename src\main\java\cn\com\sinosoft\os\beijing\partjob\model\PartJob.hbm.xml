<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
"http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<class name="cn.com.sinosoft.os.beijing.partjob.model.PartJob" table="RS_PART_JOB">
		<id name="id" column="ID" type="java.lang.String">
			<generator class="uuid"></generator>
		</id>
		<property name="applyUser" column="APPLY_USER" type="java.lang.String"/>
		<property name="applyDep" column="APPLY_DEP" type="java.lang.String"/>
		<property name="applyTime" column="APPLY_TIME" type="java.util.Date"/>
		<property name="applyAccount" column="APPLY_ACCOUNT" type="java.lang.String"/>
		<property name="partJobUnit" column="PART_JOB_UNIT" type="java.lang.String"/>
		<property name="membership" column="MEMBERSHIP" type="java.lang.String"/>
		<property name="unitAddress" column="UNIT_ADDRESS" type="java.lang.String"/>
		<property name="holdPost" column="HOLD_POST" type="java.lang.String"/>
		<property name="isReceiveSalary" column="IS_RECEIVE_SALARY" type="java.lang.String"/>
		<property name="piId" column="PI_ID" type="java.lang.String"/>
		<property name="proState" column="PRO_STATE" type="java.lang.String"/>
		<property name="addZone" column="ADD_ZONE" type="java.lang.String"/>
		<property name="addOrg" column="ADD_ORG" type="java.lang.String"/>
		<property name="addDep" column="ADD_DEP" type="java.lang.String"/>
		<property name="addUser" column="ADD_USER" type="java.lang.String"/>
		<property name="addTime" column="ADD_TIME" type="java.util.Date"/>
		<property name="modyZone" column="MODY_ZONE" type="java.lang.String"/>
		<property name="modyOrg" column="MODY_ORG" type="java.lang.String"/>
		<property name="modyDep" column="MODY_DEP" type="java.lang.String"/>
		<property name="modyUser" column="MODY_USER" type="java.lang.String"/>
		<property name="modyTime" column="MODY_TIME" type="java.util.Date"/>
		<property name="state" column="STATE" type="java.lang.String"/>
		<property name="dataSource" column="DATA_SOURCE" type="java.lang.String"/>
		<property name="dataModyTime" column="DATA_MODY_TIME" type="java.util.Date"/>
		<property name="shrUser" column="SHR_USER" type="java.lang.String"/>
		<property name="shrRemart" column="SHR_REMART" type="java.lang.String"/>
		<property name="shrDate" column="SHR_DATE" type="java.util.Date"/>
		<property name="rsbaUser" column="RSBA_USER" type="java.lang.String"/>
		<property name="rsbaRemart" column="RSBA_REMART" type="java.lang.String"/>
		<property name="rsbaDate" column="RSBA_DATE" type="java.util.Date"/>
		<property name="leader" column="LEADER" type="java.lang.String"/>
		<property name="partJobNumber" column="PART_JOB_NUMBER" type="java.lang.String"/>
		<property name="partJobExist" column="PART_JOB_EXIST" type="java.lang.String"/>
		<property name="reEmploy" column="RE_EMPLOY" type="java.lang.String"/>
	</class>
</hibernate-mapping>
